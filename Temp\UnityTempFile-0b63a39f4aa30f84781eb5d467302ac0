/target:library
/out:Temp/Unity.PlasticSCM.Editor.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Unity.PlasticSCM.Editor.dll.ref
/deterministic
/debug:portable
/optimize-
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:Library/ScriptAssemblies/UnityEditor.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:D:/2024-5-15/UnityProject/SIPSorcerytest4.2/Library/PackageCache/com.unity.collab-proxy@2.0.4/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll
/reference:D:/2024-5-15/UnityProject/SIPSorcerytest4.2/Library/PackageCache/com.unity.collab-proxy@2.0.4/Lib/Editor/PlasticSCM/unityplastic.dll
/reference:D:/2024-5-15/UnityProject/SIPSorcerytest4.2/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEditor.Graphs.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AutoStreamingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CloudFoundationModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIWidgetsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_FEATURES
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:INCLUDE_DYNAMIC_GI
/define:NET_4_6
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_48
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_IG
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
/define:UNITY_UGP_API
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\ApplicationDataPath.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssemblyInfo.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\AssetFilesFilterPatternsMenuBuilder.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\AssetMenuItems.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\AssetMenuOperations.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\AssetOperations.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\AssetsSelection.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\Dialogs\CheckinDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\Dialogs\CheckinDialogOperations.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\ProjectViewAssetSelection.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\AssetStatus.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\Cache\AssetStatusCache.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\Cache\BuildPathDictionary.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\Cache\LocalStatusCache.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\Cache\LockStatusCache.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\Cache\RemoteStatusCache.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\Cache\SearchLocks.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\DrawAssetOverlay.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\AssetsPath.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\GetSelectedPaths.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\LoadAsset.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\Processor\AssetModificationProcessor.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\Processor\AssetPostprocessor.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\Processor\AssetsProcessor.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\Processor\PlasticAssetsProcessor.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\Processor\WorkspaceOperationsMonitor.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\ProjectPath.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\RefreshAsset.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\RepaintInspector.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\SaveAssets.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AutoRefresh.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CheckWorkspaceTreeNodeStatus.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CloudProjectDownloader\CloudProjectDownloader.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CloudProjectDownloader\CommandLineArguments.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CloudProjectDownloader\DownloadRepositoryOperation.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CloudProjectDownloader\ParseArguments.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CollabMigration\MigrateCollabProject.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CollabMigration\MigrationDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CollabMigration\MigrationProgressRender.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CollabPlugin.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\AutoConfig.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\ChannelCertificateUiImpl.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\AutoLogin.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\CloudEditionWelcomeWindow.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\OrganizationPanel.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\SignInPanel.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\SignInWithEmailPanel.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\WaitingSignInPanel.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\ConfigurePartialWorkspace.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CredentialsDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CredentialsUIImpl.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\EncryptionConfigurationDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\MissingEncryptionPasswordPromptHandler.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\SSOCredentialsDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\TeamEdition\TeamEditionConfigurationWindow.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\ToolConfig.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\WriteLogConfiguration.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\CheckinProgress.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\GenericProgress.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\IncomingChangesNotifier.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\ProgressOperationHandler.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\UpdateProgress.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportLineListViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportListHeaderState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportListView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\DrawGuiModeSwitcher.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\EnumExtensions.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\FindWorkspace.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\GetRelativePath.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\CheckinProgress.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\IncomingChangesNotifier.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\ProgressOperationHandler.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\UpdateProgress.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\UpdateReport\ErrorListViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\UpdateReport\UpdateReportDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\UpdateReport\UpdateReportListHeaderState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\UpdateReport\UpdateReportListView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\BuildFormattedHelp.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\DrawHelpPanel.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\HelpData.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\HelpFormat.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\HelpLink.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\HelpLinkData.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\HelpPanel.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\TestingHelpData.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Inspector\DrawInspectorOperations.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Inspector\InspectorAssetSelection.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\MetaPath.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\NewIncomingChanges.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\ParentWindow.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticApp.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticConnectionMonitor.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticMenuItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticNotification.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticPlugin.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticPluginIsEnabledPreference.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticProjectSettingsProvider.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticWindow.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\ProjectWindow.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\QueryVisualElementsExtensions.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\SceneView\DrawSceneOperations.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\SetupCloudProjectId.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\SwitchModeConfirmationDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Tool\BringWindowToFront.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Tool\FindTool.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Tool\IsExeAvailable.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Tool\LaunchInstaller.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Tool\LaunchTool.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Tool\ToolConstants.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Avatar\ApplyCircleMask.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Avatar\AvatarImages.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Avatar\GetAvatar.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\BoolSetting.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\CloseWindowIfOpened.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\CooldownWindowDelayer.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DockEditorWindow.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawActionButton.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawActionHelpBox.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawActionToolbar.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawSearchField.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawSplitter.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawTextBlockWithEndLink.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawUserIcon.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DropDownTextField.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\EditorDispatcher.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\EditorProgressBar.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\EditorProgressControls.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\EditorVersion.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\EditorWindowFocus.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\EnumPopupSetting.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\FindEditorWindow.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\GUIActionRunner.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\GUISpace.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\GetPlasticShortcut.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\GuiEnabled.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\HandleMenuItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Images.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\MeasureMaxWidth.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Message\DrawDialogIcon.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Message\PlasticQuestionAlert.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\OverlayRect.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\PlasticDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\PlasticSplitterGUILayout.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\DrawProgressForDialogs.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\DrawProgressForMigration.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\DrawProgressForOperations.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\DrawProgressForViews.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\OperationProgressData.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\ProgressControlsForDialogs.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\ProgressControlsForMigration.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\ProgressControlsForViews.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\ResponseType.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\RunModal.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\ScreenResolution.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\ShowWindow.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\SortOrderComparer.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\StatusBar\IncomingChangesNotification.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\StatusBar\NotificationBar.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\StatusBar\StatusBar.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\TabButton.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\DrawTreeViewEmptyState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\DrawTreeViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\GetChangesOverlayIcon.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\ListViewItemIds.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\TableViewOperations.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\TreeHeaderColumns.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\TreeHeaderSettings.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\TreeViewItemIds.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UIElements\LoadingSpinner.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UIElements\ProgressControlsForDialogs.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UIElements\UIElementsExtensions.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityConstants.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityEvents.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityMenuItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityPlasticGuiMessage.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityPlasticTimer.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityStyles.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityThreadWaiter.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UnityConfigurationChecker.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\VCSPlugin.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\ViewSwitcher.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\BranchListViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\BranchesListHeaderState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\BranchesListView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\BranchesSelection.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\BranchesTab.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\BranchesViewMenu.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\CreateBranchDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\Dialogs\RenameBranchDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetListViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetsListHeaderState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetsListView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetsSelection.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetsTab.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetsTab_Operations.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetsViewMenu.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\DateFilter.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\LaunchDiffOperations.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\ConfirmContinueWithPendingChangesDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\CreateWorkspaceView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\CreateWorkspaceViewState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\CreateRepositoryDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoriesListHeaderState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoriesListView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoryExplorerDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoryListViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\DrawCreateWorkspaceView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\ValidRepositoryName.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\ChangeCategoryTreeViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\ClientDiffTreeViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\Dialogs\GetRestorePathDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\DiffPanel.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\DiffSelection.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\DiffTreeView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\DiffTreeViewMenu.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\GetClientDiffInfos.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\MergeCategoryTreeViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\UnityDiffTree.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\DownloadPlasticExeWindow.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\FileSystemOperation.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\HistoryListHeaderState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\HistoryListView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\HistoryListViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\HistoryListViewMenu.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\HistorySelection.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\HistoryTab.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\SaveAction.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\ChangeCategoryTreeViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\ChangeTreeViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\DirectoryConflicts\ConflictResolutionState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\DirectoryConflicts\DrawDirectoryResolutionPanel.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesSelection.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesTab.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesTreeHeaderState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesTreeView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesViewMenu.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IsCurrent.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IsResolved.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\UnityIncomingChangesTree.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\DrawIncomingChangesOverview.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\ChangeCategoryTreeViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\ChangeTreeViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\Errors\ErrorListViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\Errors\ErrorsListHeaderState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\Errors\ErrorsListView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesSelection.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesTab.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesTreeHeaderState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesTreeView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesViewMenu.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\UnityIncomingChangesTree.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\IIncomingChangesTab.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\ChangeCategoryTreeViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\ChangeTreeViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\ChangelistTreeViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Changelists\ChangelistMenu.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Changelists\MoveToChangelistMenuBuilder.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\CheckinConflictsDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\CreateChangelistDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\DependenciesDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\EmptyCheckinMessageDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\FilterRulesConfirmationDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\LaunchCheckinConflictsDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\LaunchDependenciesDialog.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\DrawCommentTextArea.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\FilesFilterPatternsMenuBuilder.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesMultiColumnHeader.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesSelection.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTab.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTab_Operations.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTreeHeaderState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTreeView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesViewMenu.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesViewPendingChangeMenu.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingMergeLinks\MergeLinkListViewItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingMergeLinks\MergeLinksListView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\UnityPendingChangesTree.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Welcome\DownloadAndInstallOperation.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Welcome\GetInstallerTmpFileName.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Welcome\MacOSConfigWorkaround.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Welcome\WelcomeView.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\VisualElementExtensions.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\ChangesetFromCollabCommitResponse.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\CredentialsResponse.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\CurrentUserAdminCheckResponse.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\IsCollabProjectMigratedResponse.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\OrganizationCredentials.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\TokenExchangeResponse.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\WebRestApiClient.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WorkspaceWindow.cs
