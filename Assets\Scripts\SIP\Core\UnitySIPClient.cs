using SIPSorcery.SIP.App;
using SIPSorcery.Media;
using SIPSorcery.SIP;
using SIPSorceryMedia.Abstractions;
using UnityEngine;
using System;
using System.Threading.Tasks;
using System.Net;

/// <summary>
/// Unity SIP客户端主控制器
/// 基于SIPSorcery 8.0.6和SIPSorceryMedia.FFmpeg实现
/// </summary>
public class UnitySIPClient : MonoBehaviour
{
    [Header("SIP Configuration")]
    [SerializeField] private SIPClientConfig sipConfig;
    
    [Header("Media Components")]
    [SerializeField] private UnityAudioEndPoint audioEndPoint;
    
    [Header("UI Components")]
    [SerializeField] private UISIPPanel uiPanel;
    
    // SIP核心组件
    private SIPTransport _sipTransport;
    private SIPUserAgent _userAgent;
    private SIPRegistrationUserAgent _registrationAgent;
    private VoIPMediaSession _mediaSession;
    
    // 状态管理
    private SIPClientState _currentState = SIPClientState.Uninitialized;
    private bool _isRegistered = false;
    private bool _isInCall = false;
    
    // 事件
    public event Action<SIPClientState> StateChanged;
    public event Action<bool> RegistrationChanged;
    public event Action<bool> CallStateChanged;
    
    #region Unity Lifecycle
    
    private async void Start()
    {
        // 如果没有配置文件，尝试从Resources加载默认配置
        if (sipConfig == null)
        {
            sipConfig = Resources.Load<SIPClientConfig>("DefaultSIPConfig");
            if (sipConfig == null)
            {
                Debug.LogError("未找到SIP配置文件！请在Inspector中指定sipConfig或在Resources文件夹中创建DefaultSIPConfig.asset");
                return;
            }
            Debug.Log("自动加载了默认SIP配置文件");
        }

        await InitializeAsync();
    }
    
    private void OnDestroy()
    {
        CleanupResources();
    }
    
    #endregion
    
    #region Initialization
    
    /// <summary>
    /// 异步初始化SIP客户端
    /// </summary>
    private async Task InitializeAsync()
    {
        try
        {
            ChangeState(SIPClientState.Initializing);
            
            // 验证配置
            if (!ValidateConfiguration())
            {
                throw new InvalidOperationException("Invalid SIP configuration");
            }
            
            // 初始化媒体端点
            await InitializeMediaEndPoints();
            
            // 初始化SIP传输层
            InitializeSIPTransport();
            
            // 初始化用户代理
            InitializeUserAgent();
            
            // 初始化UI
            InitializeUI();
            
            ChangeState(SIPClientState.Initialized);
            Debug.Log("Unity SIP Client initialized successfully");
            
            // 自动注册（如果配置了）
            if (sipConfig.AutoRegister)
            {
                await RegisterAsync();
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to initialize SIP client: {ex.Message}");
            ChangeState(SIPClientState.Error);
        }
    }
    
    /// <summary>
    /// 初始化媒体端点
    /// </summary>
    private Task InitializeMediaEndPoints()
    {
        try
        {
            // 获取或创建音频端点
            if (audioEndPoint == null)
            {
                audioEndPoint = GetComponent<UnityAudioEndPoint>();
                if (audioEndPoint == null)
                {
                    audioEndPoint = gameObject.AddComponent<UnityAudioEndPoint>();
                }
            }
            
            // 创建媒体会话
            var mediaEndPoints = new MediaEndPoints();

            if (audioEndPoint != null)
            {
                mediaEndPoints.AudioSource = audioEndPoint;
                mediaEndPoints.AudioSink = audioEndPoint;
            }
            
            _mediaSession = new VoIPMediaSession(mediaEndPoints);
            
            Debug.Log("Media endpoints initialized successfully");
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to initialize media endpoints: {ex.Message}");
            return Task.FromException(ex);
        }
    }
    
    /// <summary>
    /// 初始化SIP传输层
    /// </summary>
    private void InitializeSIPTransport()
    {
        try
        {
            _sipTransport = new SIPTransport();
            
            // 根据配置选择传输协议
            if (sipConfig.UseUDP)
            {
                var udpChannel = new SIPUDPChannel(new IPEndPoint(IPAddress.Any, 0));
                _sipTransport.AddSIPChannel(udpChannel);
                Debug.Log("SIP UDP transport initialized");
            }
            
            if (sipConfig.UseTCP)
            {
                var tcpChannel = new SIPTCPChannel(new IPEndPoint(IPAddress.Any, 0));
                _sipTransport.AddSIPChannel(tcpChannel);
                Debug.Log("SIP TCP transport initialized");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to initialize SIP transport: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// 初始化SIP用户代理
    /// </summary>
    private void InitializeUserAgent()
    {
        try
        {
            var serverEndPoint = SIPEndPoint.ParseSIPEndPoint($"{sipConfig.ProxyServer}:{sipConfig.ProxyPort}");
            _userAgent = new SIPUserAgent(_sipTransport, serverEndPoint);
            
            // 绑定事件
            _userAgent.OnIncomingCall += OnIncomingCall;
            _userAgent.OnCallHungup += OnCallHungup;
            _userAgent.ClientCallFailed += OnCallFailed;
            
            Debug.Log("SIP user agent initialized");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to initialize user agent: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// 初始化UI
    /// </summary>
    private void InitializeUI()
    {
        if (uiPanel != null)
        {
            uiPanel.Initialize(this);
            uiPanel.UpdateState(_currentState);
        }
    }
    
    /// <summary>
    /// 验证配置
    /// </summary>
    private bool ValidateConfiguration()
    {
        if (sipConfig == null)
        {
            Debug.LogError("SIP configuration is null");
            return false;
        }
        
        if (string.IsNullOrEmpty(sipConfig.ProxyServer))
        {
            Debug.LogError("Proxy server not configured");
            return false;
        }
        
        if (string.IsNullOrEmpty(sipConfig.Username))
        {
            Debug.LogError("Username not configured");
            return false;
        }
        
        return true;
    }
    
    #endregion
    
    #region SIP Registration
    
    /// <summary>
    /// 异步注册到SIP服务器
    /// </summary>
    public async Task<bool> RegisterAsync()
    {
        if (_currentState != SIPClientState.Initialized && _currentState != SIPClientState.RegistrationFailed)
        {
            Debug.LogWarning($"Cannot register in current state: {_currentState}");
            return false;
        }
        
        try
        {
            ChangeState(SIPClientState.Registering);
            
            var registrarEndPoint = SIPEndPoint.ParseSIPEndPoint($"{sipConfig.ProxyServer}:{sipConfig.ProxyPort}");
            var fromUri = new SIPURI(sipConfig.Username, sipConfig.Domain, null);
            var contactUri = new SIPURI(sipConfig.Username, GetLocalIP(), null);
            
            _registrationAgent = new SIPRegistrationUserAgent(
                _sipTransport,
                registrarEndPoint,
                fromUri,
                sipConfig.Username,
                sipConfig.Password,
                sipConfig.Username,
                null,
                contactUri,
                sipConfig.ExpireTime,
                null,
                sipConfig.RegistrationTimeout,
                5000,
                3,
                true
            );
            
            _registrationAgent.RegistrationSuccessful += OnRegistrationSuccessful;
            _registrationAgent.RegistrationFailed += OnRegistrationFailed;
            
            _registrationAgent.Start();
            
            // 等待注册结果
            var timeout = TimeSpan.FromMilliseconds(sipConfig.RegistrationTimeout);
            var startTime = DateTime.Now;
            
            while (!_isRegistered && DateTime.Now - startTime < timeout && _currentState == SIPClientState.Registering)
            {
                await Task.Delay(100);
            }
            
            return _isRegistered;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Registration failed: {ex.Message}");
            ChangeState(SIPClientState.RegistrationFailed);
            return false;
        }
    }
    
    /// <summary>
    /// 注销
    /// </summary>
    public Task<bool> UnregisterAsync()
    {
        if (_registrationAgent != null && _isRegistered)
        {
            try
            {
                _registrationAgent.Stop();
                _isRegistered = false;
                RegistrationChanged?.Invoke(false);
                ChangeState(SIPClientState.Initialized);
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Unregistration failed: {ex.Message}");
                return Task.FromResult(false);
            }
        }
        return Task.FromResult(true);
    }
    
    #endregion
    
    #region Call Management
    
    /// <summary>
    /// 发起呼叫
    /// </summary>
    public async Task<bool> MakeCallAsync(string destination)
    {
        if (!_isRegistered)
        {
            Debug.LogWarning("Not registered, cannot make call");
            return false;
        }
        
        if (_isInCall)
        {
            Debug.LogWarning("Already in call");
            return false;
        }
        
        try
        {
            ChangeState(SIPClientState.Calling);
            _isInCall = true;
            CallStateChanged?.Invoke(true);
            
            // 启动媒体会话
            await _mediaSession.Start();
            
            // 发起呼叫
            bool result = await _userAgent.Call(destination, null, null, _mediaSession);
            
            if (result)
            {
                ChangeState(SIPClientState.InCall);
                Debug.Log($"Call to {destination} successful");
            }
            else
            {
                await EndCall();
                Debug.LogError($"Call to {destination} failed");
            }
            
            return result;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Make call failed: {ex.Message}");
            await EndCall();
            return false;
        }
    }
    
    /// <summary>
    /// 接听来电
    /// </summary>
    public async Task<bool> AnswerCallAsync()
    {
        if (_currentState != SIPClientState.Ringing)
        {
            Debug.LogWarning($"Cannot answer call in current state: {_currentState}");
            return false;
        }

        try
        {
            ChangeState(SIPClientState.InCall);
            _isInCall = true;
            CallStateChanged?.Invoke(true);

            // 启动媒体会话
            await _mediaSession.Start();

            // 接听呼叫 - 这里需要根据实际的来电处理逻辑来实现
            // 通常在OnIncomingCall中会创建一个UAS (User Agent Server)
            // 这里简化处理，假设_userAgent已经准备好接听

            Debug.Log("Call answered successfully");
            return true;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Answer call failed: {ex.Message}");
            await EndCall();
            return false;
        }
    }

    /// <summary>
    /// 挂断电话
    /// </summary>
    public async Task<bool> HangupCallAsync()
    {
        return await EndCall();
    }
    
    /// <summary>
    /// 结束通话
    /// </summary>
    private Task<bool> EndCall()
    {
        try
        {
            if (_userAgent != null && _userAgent.IsCallActive)
            {
                _userAgent.Hangup();
            }
            
            if (_mediaSession != null)
            {
                _mediaSession.Close("Call ended");
            }
            
            _isInCall = false;
            CallStateChanged?.Invoke(false);
            
            if (_isRegistered)
            {
                ChangeState(SIPClientState.Registered);
            }
            else
            {
                ChangeState(SIPClientState.Initialized);
            }
            
            Debug.Log("Call ended");
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            Debug.LogError($"End call failed: {ex.Message}");
            return Task.FromResult(false);
        }
    }
    
    #endregion

    #region Event Handlers

    private void OnRegistrationSuccessful(SIPURI uri, SIPResponse response)
    {
        _isRegistered = true;
        RegistrationChanged?.Invoke(true);
        ChangeState(SIPClientState.Registered);
        Debug.Log($"Registration successful: {uri}");
    }

    private void OnRegistrationFailed(SIPURI uri, SIPResponse response, string errorMessage)
    {
        _isRegistered = false;
        RegistrationChanged?.Invoke(false);
        ChangeState(SIPClientState.RegistrationFailed);
        Debug.LogError($"Registration failed: {errorMessage}");
    }

    private async void OnIncomingCall(SIPUserAgent userAgent, SIPRequest sipRequest)
    {
        try
        {
            if (_isInCall)
            {
                // 拒绝新的来电
                var uas = _userAgent.AcceptCall(sipRequest);
                uas.Reject(SIPResponseStatusCodesEnum.BusyHere, null);
                return;
            }

            ChangeState(SIPClientState.Ringing);
            Debug.Log("Incoming call received");

            // 通知UI显示来电
            if (uiPanel != null)
            {
                uiPanel.ShowIncomingCall(sipRequest.Header.From.FromURI.User);
            }

            // 如果配置了自动接听，则自动接听来电
            if (sipConfig != null && sipConfig.AutoAnswer)
            {
                Debug.Log("Auto-answering incoming call");
                await Task.Delay(1000); // 短暂延迟以确保UI更新
                await AnswerCallAsync();
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error handling incoming call: {ex.Message}");
            // 确保状态一致性
            if (_currentState == SIPClientState.Ringing)
            {
                ChangeState(_isRegistered ? SIPClientState.Registered : SIPClientState.Initialized);
            }
        }
    }

    private async void OnCallHungup(SIPDialogue dialogue)
    {
        await EndCall();
        Debug.Log("Call hung up by remote party");
    }

    private void OnCallFailed(ISIPClientUserAgent userAgent, string errorMessage, SIPResponse sipResponse)
    {
        Debug.LogError($"Call failed: {errorMessage}");
        _ = EndCall();
    }

    #endregion

    #region State Management

    private void ChangeState(SIPClientState newState)
    {
        if (_currentState != newState)
        {
            var oldState = _currentState;
            _currentState = newState;
            StateChanged?.Invoke(newState);

            Debug.Log($"State changed: {oldState} -> {newState}");

            // 更新UI
            if (uiPanel != null)
            {
                uiPanel.UpdateState(newState);
            }
        }
    }

    #endregion

    #region Utility Methods

    private string GetLocalIP()
    {
        if (!string.IsNullOrEmpty(sipConfig.LocalIP))
            return sipConfig.LocalIP;

        try
        {
            using (var socket = new System.Net.Sockets.Socket(System.Net.Sockets.AddressFamily.InterNetwork,
                                                             System.Net.Sockets.SocketType.Dgram, 0))
            {
                socket.Connect("*******", 65530);
                var endPoint = socket.LocalEndPoint as IPEndPoint;
                return endPoint?.Address.ToString() ?? "127.0.0.1";
            }
        }
        catch
        {
            return "127.0.0.1";
        }
    }

    private void CleanupResources()
    {
        try
        {
            _registrationAgent?.Stop();
            _userAgent?.Dispose();
            _sipTransport?.Dispose();
            _mediaSession?.Close("Application closing");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error during cleanup: {ex.Message}");
        }
    }

    #endregion

    #region Public Properties

    public SIPClientState CurrentState => _currentState;
    public bool IsRegistered => _isRegistered;
    public bool IsInCall => _isInCall;

    #endregion
}
