using UnityEngine;
using UnityEngine.UI;
using System;

/// <summary>
/// SIP客户端UI面板
/// </summary>
public class UISIPPanel : MonoBehaviour
{
    [Header("Status Display")]
    [SerializeField] private Text statusText;
    [SerializeField] private Text registrationStatusText;
    [SerializeField] private Image statusIndicator;
    
    [Header("Call Controls")]
    [SerializeField] private InputField destinationInput;
    [SerializeField] private Button callButton;
    [SerializeField] private Button answerButton;
    [SerializeField] private Button hangupButton;
    [SerializeField] private Button registerButton;
    
    [Header("Incoming Call UI")]
    [SerializeField] private GameObject incomingCallPanel;
    [SerializeField] private Text incomingCallerText;
    
    [Header("Status Colors")]
    [SerializeField] private Color unregisteredColor = Color.red;
    [SerializeField] private Color registeredColor = Color.green;
    [SerializeField] private Color callingColor = Color.yellow;
    [SerializeField] private Color inCallColor = Color.blue;
    
    private UnitySIPClient _sipClient;
    
    public void Initialize(UnitySIPClient sipClient)
    {
        _sipClient = sipClient;
        
        // 绑定按钮事件
        if (callButton != null)
            callButton.onClick.AddListener(OnCallButtonClicked);
        if (answerButton != null)
            answerButton.onClick.AddListener(OnAnswerButtonClicked);
        if (hangupButton != null)
            hangupButton.onClick.AddListener(OnHangupButtonClicked);
        if (registerButton != null)
            registerButton.onClick.AddListener(OnRegisterButtonClicked);
        
        // 订阅SIP客户端事件
        _sipClient.StateChanged += OnStateChanged;
        _sipClient.RegistrationChanged += OnRegistrationChanged;
        _sipClient.CallStateChanged += OnCallStateChanged;
        
        // 初始化UI状态
        UpdateButtonStates();
        HideIncomingCall();
    }
    
    public void UpdateState(SIPClientState state)
    {
        if (statusText != null)
        {
            statusText.text = GetStateDisplayText(state);
        }
        
        if (statusIndicator != null)
        {
            statusIndicator.color = GetStateColor(state);
        }
        
        UpdateButtonStates();
    }
    
    public void ShowIncomingCall(string callerName)
    {
        if (incomingCallPanel != null)
        {
            incomingCallPanel.SetActive(true);
        }
        
        if (incomingCallerText != null)
        {
            incomingCallerText.text = $"来电: {callerName}";
        }
    }
    
    public void HideIncomingCall()
    {
        if (incomingCallPanel != null)
        {
            incomingCallPanel.SetActive(false);
        }
    }
    
    private void OnStateChanged(SIPClientState state)
    {
        UpdateState(state);
    }
    
    private void OnRegistrationChanged(bool isRegistered)
    {
        if (registrationStatusText != null)
        {
            registrationStatusText.text = isRegistered ? "已注册" : "未注册";
            registrationStatusText.color = isRegistered ? registeredColor : unregisteredColor;
        }
        
        UpdateButtonStates();
    }
    
    private void OnCallStateChanged(bool isInCall)
    {
        UpdateButtonStates();
        
        if (!isInCall)
        {
            HideIncomingCall();
        }
    }
    
    private async void OnCallButtonClicked()
    {
        if (_sipClient != null && destinationInput != null && !string.IsNullOrEmpty(destinationInput.text))
        {
            await _sipClient.MakeCallAsync(destinationInput.text);
        }
    }
    
    private async void OnAnswerButtonClicked()
    {
        if (_sipClient != null)
        {
            // 这里需要实现接听逻辑
            HideIncomingCall();
        }
    }
    
    private async void OnHangupButtonClicked()
    {
        if (_sipClient != null)
        {
            await _sipClient.HangupCallAsync();
        }
    }
    
    private async void OnRegisterButtonClicked()
    {
        if (_sipClient != null)
        {
            if (_sipClient.IsRegistered)
            {
                await _sipClient.UnregisterAsync();
            }
            else
            {
                await _sipClient.RegisterAsync();
            }
        }
    }
    
    private void UpdateButtonStates()
    {
        if (_sipClient == null) return;
        
        var state = _sipClient.CurrentState;
        var isRegistered = _sipClient.IsRegistered;
        var isInCall = _sipClient.IsInCall;
        
        // 更新按钮可用状态
        if (callButton != null)
            callButton.interactable = isRegistered && !isInCall;
        
        if (answerButton != null)
            answerButton.interactable = state == SIPClientState.Ringing;
        
        if (hangupButton != null)
            hangupButton.interactable = isInCall || state == SIPClientState.Calling || state == SIPClientState.Ringing;
        
        if (registerButton != null)
        {
            registerButton.interactable = state == SIPClientState.Initialized || 
                                         state == SIPClientState.Registered || 
                                         state == SIPClientState.RegistrationFailed;
            
            var buttonText = registerButton.GetComponentInChildren<Text>();
            if (buttonText != null)
            {
                buttonText.text = isRegistered ? "注销" : "注册";
            }
        }
    }
    
    private string GetStateDisplayText(SIPClientState state)
    {
        switch (state)
        {
            case SIPClientState.Uninitialized: return "未初始化";
            case SIPClientState.Initializing: return "初始化中...";
            case SIPClientState.Initialized: return "已初始化";
            case SIPClientState.Registering: return "注册中...";
            case SIPClientState.Registered: return "已注册";
            case SIPClientState.RegistrationFailed: return "注册失败";
            case SIPClientState.Calling: return "呼叫中...";
            case SIPClientState.Ringing: return "来电中";
            case SIPClientState.InCall: return "通话中";
            case SIPClientState.Error: return "错误";
            default: return "未知状态";
        }
    }
    
    private Color GetStateColor(SIPClientState state)
    {
        switch (state)
        {
            case SIPClientState.Registered:
                return registeredColor;
            case SIPClientState.Calling:
            case SIPClientState.Ringing:
                return callingColor;
            case SIPClientState.InCall:
                return inCallColor;
            case SIPClientState.Error:
            case SIPClientState.RegistrationFailed:
                return unregisteredColor;
            default:
                return Color.gray;
        }
    }
    
    private void OnDestroy()
    {
        if (_sipClient != null)
        {
            _sipClient.StateChanged -= OnStateChanged;
            _sipClient.RegistrationChanged -= OnRegistrationChanged;
            _sipClient.CallStateChanged -= OnCallStateChanged;
        }
    }
}
