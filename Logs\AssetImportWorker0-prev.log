Using pre-set license
Built from '2020.3/china_unity/release' branch; Version is '2020.3.48f1c1 (06fbdfbf16e3) revision 457695'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 16290 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
D:\Unity\2020.3.48f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/2024-5-15/UnityProject/SIPSorcerytest4.2
-logFile
Logs/AssetImportWorker0.log
-srvPort
9986
Successfully changed project path to: D:/2024-5-15/UnityProject/SIPSorcerytest4.2
D:/2024-5-15/UnityProject/SIPSorcerytest4.2
Using Asset Import Pipeline V2.
Player connection [19836] Host "[IP] ************* [Port] 805523936 [Flags] 2 [Guid] 737408629 [EditorId] 737408629 [Version] 1048832 [Id] WindowsEditor(7,xxzx-bzhzy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [19836] Host "[IP] ************* [Port] 805523936 [Flags] 2 [Guid] 737408629 [EditorId] 737408629 [Version] 1048832 [Id] WindowsEditor(7,xxzx-bzhzy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.Refreshing native plugins compatible for Editor in 41.64 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.48f1c1 (06fbdfbf16e3)
[Subsystems] Discovering subsystems at path D:/Unity/2020.3.48f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/2024-5-15/UnityProject/SIPSorcerytest4.2/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA GeForce GT 730 (ID=0x1287)
    Vendor:   
    VRAM:     2007 MB
    Driver:   30.0.14.7514
Initialize mono
Mono path[0] = 'D:/Unity/2020.3.48f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56472
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.005423 seconds.
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 33.35 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.803 seconds
Domain Reload Profiling:
	ReloadAssembly (803ms)
		BeginReloadAssembly (104ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (606ms)
			LoadAssemblies (103ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (174ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (44ms)
			SetupLoadedEditorAssemblies (249ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (33ms)
				BeforeProcessingInitializeOnLoad (21ms)
				ProcessInitializeOnLoadAttributes (136ms)
				ProcessInitializeOnLoadMethodAttributes (49ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.022220 seconds.
Begin MonoManager ReloadAssembly
Error: Could not load signature of SIPSorceryMedia.FFmpeg.FFmpegAudioDecoder:.ctor due to: Could not load file or assembly 'FFmpeg.AutoGen, Version=*******, Culture=neutral, PublicKeyToken=null' or one of its dependencies. assembly:FFmpeg.AutoGen, Version=*******, Culture=neutral, PublicKeyToken=null type:<unknown type> member:(null) signature:<none>
Unloading broken assembly Assets/Plugins/SIPSorceryMedia.FFmpeg.dll, this assembly can cause crashes in the runtime
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.38 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.470 seconds
Domain Reload Profiling:
	ReloadAssembly (1470ms)
		BeginReloadAssembly (208ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (1187ms)
			LoadAssemblies (167ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (355ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (544ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (150ms)
				ProcessInitializeOnLoadAttributes (355ms)
				ProcessInitializeOnLoadMethodAttributes (14ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.22 seconds
Refreshing native plugins compatible for Editor in 0.36 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1348 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 64.7 MB.
System memory in use after: 64.8 MB.

Unloading 30 unused Assets to reduce memory usage. Loaded Objects now: 1774.
Total: 3.610400 ms (FindLiveObjects: 0.225300 ms CreateObjectMapping: 0.127200 ms MarkObjects: 3.177900 ms  DeleteObjects: 0.078600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Resources/DefaultSIPConfig.asset
  artifactKey: Guid(781e4105998aac949bd0648cbb5cc15c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/DefaultSIPConfig.asset using Guid(781e4105998aac949bd0648cbb5cc15c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) Could not extract GUID in text file Assets/Resources/DefaultSIPConfig.asset at line 12.
Broken text PPtr. GUID 00000000000000000000000000000000 fileID 11500000 is invalid!
 -> (artifact id: 'df6a42dc6602c43fc2f875f035d556c5') in 0.097326 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.053428 seconds.
Begin MonoManager ReloadAssembly
Error: Could not load signature of SIPSorceryMedia.FFmpeg.FFmpegAudioSource:add_OnAudioSourceEncodedFrameReady due to: Could not resolve type with token 01000037 (from typeref, class/assembly SIPSorceryMedia.Abstractions.EncodedAudioFrame, SIPSorceryMedia.Abstractions, Version=********, Culture=neutral, PublicKeyToken=null) assembly:SIPSorceryMedia.Abstractions, Version=********, Culture=neutral, PublicKeyToken=null type:SIPSorceryMedia.Abstractions.EncodedAudioFrame member:(null) signature:<none>
Unloading broken assembly Assets/Plugins/SIPSorceryMedia.FFmpeg.dll, this assembly can cause crashes in the runtime
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.39 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  7.029 seconds
Domain Reload Profiling:
	ReloadAssembly (7262ms)
		BeginReloadAssembly (5226ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (744ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (5ms)
			CreateAndSetChildDomain (2611ms)
		EndReloadAssembly (1815ms)
			LoadAssemblies (675ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (627ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (63ms)
			SetupLoadedEditorAssemblies (583ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (28ms)
				RefreshPlugins (0ms)
				BeforeProcessingInitializeOnLoad (125ms)
				ProcessInitializeOnLoadAttributes (388ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (9ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.83 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1343 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 64.7 MB.
System memory in use after: 64.8 MB.

Unloading 26 unused Assets to reduce memory usage. Loaded Objects now: 1778.
Total: 2.752000 ms (FindLiveObjects: 0.126800 ms CreateObjectMapping: 0.043400 ms MarkObjects: 2.514000 ms  DeleteObjects: 0.066400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 141377.814143 seconds.
  path: Assets/Scripts/SIP/Core
  artifactKey: Guid(5939509d70140dd428db5bb67cb7a140) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/SIP/Core using Guid(5939509d70140dd428db5bb67cb7a140) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '49fdf2d9e663f638f9d92c740c01f65a') in 0.152224 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.191381 seconds.
Begin MonoManager ReloadAssembly
Error: Could not load signature of SIPSorceryMedia.FFmpeg.FFmpegAudioSource:add_OnAudioSourceEncodedFrameReady due to: Could not resolve type with token 01000037 (from typeref, class/assembly SIPSorceryMedia.Abstractions.EncodedAudioFrame, SIPSorceryMedia.Abstractions, Version=********, Culture=neutral, PublicKeyToken=null) assembly:SIPSorceryMedia.Abstractions, Version=********, Culture=neutral, PublicKeyToken=null type:SIPSorceryMedia.Abstractions.EncodedAudioFrame member:(null) signature:<none>
Unloading broken assembly Assets/Plugins/SIPSorceryMedia.FFmpeg.dll, this assembly can cause crashes in the runtime
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.41 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.530 seconds
Domain Reload Profiling:
	ReloadAssembly (1531ms)
		BeginReloadAssembly (177ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (43ms)
		EndReloadAssembly (1270ms)
			LoadAssemblies (191ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (423ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (52ms)
			SetupLoadedEditorAssemblies (476ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (111ms)
				ProcessInitializeOnLoadAttributes (330ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.42 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1343 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 64.7 MB.
System memory in use after: 64.8 MB.

Unloading 26 unused Assets to reduce memory usage. Loaded Objects now: 1781.
Total: 6.517800 ms (FindLiveObjects: 0.414100 ms CreateObjectMapping: 0.437800 ms MarkObjects: 5.553200 ms  DeleteObjects: 0.111200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 124.605791 seconds.
  path: Assets/Scripts/SIP/Core/SIPClientConfig.cs
  artifactKey: Guid(26992066689e38f43b7424a9abb30144) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/SIP/Core/SIPClientConfig.cs using Guid(26992066689e38f43b7424a9abb30144) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '858678210fb0780453f1fa1bafa3003c') in 0.230814 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.019942 seconds.
Begin MonoManager ReloadAssembly
Error: Could not load signature of SIPSorceryMedia.FFmpeg.FFmpegAudioSource:add_OnAudioSourceEncodedFrameReady due to: Could not resolve type with token 01000037 (from typeref, class/assembly SIPSorceryMedia.Abstractions.EncodedAudioFrame, SIPSorceryMedia.Abstractions, Version=********, Culture=neutral, PublicKeyToken=null) assembly:SIPSorceryMedia.Abstractions, Version=********, Culture=neutral, PublicKeyToken=null type:SIPSorceryMedia.Abstractions.EncodedAudioFrame member:(null) signature:<none>
Unloading broken assembly Assets/Plugins/SIPSorceryMedia.FFmpeg.dll, this assembly can cause crashes in the runtime
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.38 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.524 seconds
Domain Reload Profiling:
	ReloadAssembly (1526ms)
		BeginReloadAssembly (249ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (36ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (87ms)
		EndReloadAssembly (1208ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (552ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (53ms)
			SetupLoadedEditorAssemblies (385ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (9ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (0ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (261ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (9ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.42 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1343 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 64.8 MB.
System memory in use after: 64.9 MB.

Unloading 26 unused Assets to reduce memory usage. Loaded Objects now: 1784.
Total: 2.603300 ms (FindLiveObjects: 0.133600 ms CreateObjectMapping: 0.047200 ms MarkObjects: 2.373600 ms  DeleteObjects: 0.048100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 4412.834057 seconds.
  path: Assets/Scripts/SIP/Core/SIPClientConfig.asset
  artifactKey: Guid(4a315e64c9e2a1c48b43b85d5a0c16a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/SIP/Core/SIPClientConfig.asset using Guid(4a315e64c9e2a1c48b43b85d5a0c16a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '28c370f8d42fb4cf7e2593bd10c656ec') in 0.137527 seconds 
========================================================================
Received Import Request.
  Time since last request: 7.981777 seconds.
  path: Assets/Scripts/SIP/Core/UnitySIPClient.cs
  artifactKey: Guid(2fce36bc2b275814fa18e1631cfe5f57) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/SIP/Core/UnitySIPClient.cs using Guid(2fce36bc2b275814fa18e1631cfe5f57) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '34ee781fe3560a544ec594e38a1fa8df') in 0.013933 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.693993 seconds.
  path: Assets/Scripts/SIP/Core/SIPClientState.cs
  artifactKey: Guid(1bdf931d7011bab4585567504dbd7155) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/SIP/Core/SIPClientState.cs using Guid(1bdf931d7011bab4585567504dbd7155) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9c4900e609a367829e82dc7c1d297026') in 0.009490 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.302982 seconds.
  path: Assets/Scripts/SIP/UI/UISIPPanel.cs
  artifactKey: Guid(17349212529bfec4e8d8c079bf9c2acb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/SIP/UI/UISIPPanel.cs using Guid(17349212529bfec4e8d8c079bf9c2acb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5dcd01c9f5f69f56692f940329e4b9b5') in 0.006335 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.033376 seconds.
Begin MonoManager ReloadAssembly
Error: Could not load signature of SIPSorceryMedia.FFmpeg.FFmpegAudioSource:add_OnAudioSourceEncodedFrameReady due to: Could not resolve type with token 01000037 (from typeref, class/assembly SIPSorceryMedia.Abstractions.EncodedAudioFrame, SIPSorceryMedia.Abstractions, Version=********, Culture=neutral, PublicKeyToken=null) assembly:SIPSorceryMedia.Abstractions, Version=********, Culture=neutral, PublicKeyToken=null type:SIPSorceryMedia.Abstractions.EncodedAudioFrame member:(null) signature:<none>
Unloading broken assembly Assets/Plugins/SIPSorceryMedia.FFmpeg.dll, this assembly can cause crashes in the runtime
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.38 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.403 seconds
Domain Reload Profiling:
	ReloadAssembly (1404ms)
		BeginReloadAssembly (170ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (16ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (39ms)
		EndReloadAssembly (1163ms)
			LoadAssemblies (179ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (325ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (44ms)
			SetupLoadedEditorAssemblies (555ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (0ms)
				BeforeProcessingInitializeOnLoad (114ms)
				ProcessInitializeOnLoadAttributes (413ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (9ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.37 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1343 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 64.8 MB.
System memory in use after: 64.9 MB.

Unloading 26 unused Assets to reduce memory usage. Loaded Objects now: 1787.
Total: 2.635600 ms (FindLiveObjects: 0.140400 ms CreateObjectMapping: 0.056200 ms MarkObjects: 2.386700 ms  DeleteObjects: 0.051200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.023015 seconds.
Begin MonoManager ReloadAssembly
Error: Could not load signature of SIPSorceryMedia.FFmpeg.FFmpegAudioSource:add_OnAudioSourceEncodedFrameReady due to: Could not resolve type with token 01000037 (from typeref, class/assembly SIPSorceryMedia.Abstractions.EncodedAudioFrame, SIPSorceryMedia.Abstractions, Version=********, Culture=neutral, PublicKeyToken=null) assembly:SIPSorceryMedia.Abstractions, Version=********, Culture=neutral, PublicKeyToken=null type:SIPSorceryMedia.Abstractions.EncodedAudioFrame member:(null) signature:<none>
Unloading broken assembly Assets/Plugins/SIPSorceryMedia.FFmpeg.dll, this assembly can cause crashes in the runtime
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.40 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.506 seconds
Domain Reload Profiling:
	ReloadAssembly (1510ms)
		BeginReloadAssembly (178ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (38ms)
		EndReloadAssembly (1252ms)
			LoadAssemblies (179ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (393ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (94ms)
			SetupLoadedEditorAssemblies (509ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (0ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (375ms)
				ProcessInitializeOnLoadMethodAttributes (13ms)
				AfterProcessingInitializeOnLoad (9ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.38 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1343 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 64.8 MB.
System memory in use after: 64.9 MB.

Unloading 26 unused Assets to reduce memory usage. Loaded Objects now: 1790.
Total: 3.117600 ms (FindLiveObjects: 0.117700 ms CreateObjectMapping: 0.044400 ms MarkObjects: 2.904300 ms  DeleteObjects: 0.050300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
