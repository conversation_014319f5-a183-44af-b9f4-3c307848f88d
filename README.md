# Unity SIP客户端项目

## 📋 项目概述

基于Unity 2020.3.48f1c1开发的SIP客户端，使用SIPSorcery 8.0.6和SIPSorceryMedia.FFmpeg实现高质量的音视频通话功能。

## 🏗️ 项目架构

### 技术栈
- **Unity**: 2020.3.48f1c1
- **SIPSorcery**: 8.0.6 (SIP协议 + 基础编解码器)
- **SIPSorceryMedia.FFmpeg**: 1.2.1 (音视频编解码器)
- **FFmpeg**: 本地命令行工具
- **.NET Framework**: 4.8

### 架构层次
```
Unity SIP Client
├── Core Layer (核心层)
│   ├── UnitySIPClient (主控制器)
│   ├── SIPClientConfig (配置管理)
│   └── SIPClientState (状态管理)
├── Media Layer (媒体层)
│   ├── UnityAudioEndPoint (音频端点)
│   ├── UnityVideoEndPoint (视频端点)
│   └── UnityMainThreadDispatcher (线程调度)
├── SIP Layer (SIP协议层)
│   └── SIPSorcery库提供的完整SIP协议栈
└── UI Layer (界面层)
    └── UISIPPanel (界面管理)
```

## 🚀 快速开始

### 环境要求
- Unity 2020.3.48f1c1或更高版本
- .NET Framework 4.8支持
- FFmpeg命令行工具已安装
- Windows 10/11操作系统

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd SIPSorcerytest4.2
   ```

2. **验证FFmpeg安装**
   ```bash
   ffmpeg -version
   ```

3. **打开Unity项目**
   - 使用Unity 2020.3.48f1c1打开项目
   - 等待依赖包自动导入

4. **配置SIP服务器**
   - 在Unity Inspector中配置SIPClientConfig
   - 设置SIP服务器地址、用户名、密码等

5. **运行项目**
   - 打开SIPClientDemo场景
   - 点击Play按钮开始测试

## 📦 依赖包

### NuGet包依赖
- SIPSorcery 8.0.6
- SIPSorceryMedia.FFmpeg 1.2.1
- SIPSorceryMedia.Abstractions 1.2.1
- Microsoft.Extensions.Logging.Abstractions 8.0.0
- System.Threading.Tasks.Extensions 4.5.4

### Unity包依赖
- Unity Audio系统
- Unity Video系统
- Unity UI系统

## 🎯 功能特性

### 已实现功能
- ✅ SIP注册和认证
- ✅ 音频通话（G.711、G.722支持）
- ✅ 基础视频通话框架
- ✅ 状态管理和UI显示
- ✅ 错误处理和日志记录

### 计划功能
- 🔄 完整的视频编解码器集成
- 🔄 OPUS音频编解码器支持
- 🔄 H.264/VP8视频编解码器支持
- 🔄 NAT穿透（STUN/TURN）
- 🔄 多方通话支持

## 🔧 配置说明

### SIP配置
```csharp
// 在Unity Inspector中配置SIPClientConfig
ProxyServer: "sip.example.com"
ProxyPort: 5060
Username: "your_username"
Password: "your_password"
Domain: "example.com"
```

### 音频配置
```csharp
// 音频设置
SampleRate: 48000
Channels: 1
CaptureInterval: 0.02f (20ms)
```

### 视频配置
```csharp
// 视频设置
VideoWidth: 640
VideoHeight: 480
FrameRate: 30
```

## 📁 项目结构

```
Assets/Scripts/
├── SIP/
│   ├── Core/
│   │   ├── UnitySIPClient.cs      (主控制器)
│   │   ├── SIPClientConfig.cs     (配置管理)
│   │   └── SIPClientState.cs      (状态枚举)
│   ├── Media/
│   │   ├── UnityAudioEndPoint.cs  (音频端点)
│   │   └── UnityVideoEndPoint.cs  (视频端点)
│   └── UI/
│       └── UISIPPanel.cs          (UI管理)
├── Utils/
│   └── UnityMainThreadDispatcher.cs (线程调度)
└── Tests/
    └── RefactorValidation.cs       (重构验证)
```

## 🧪 测试

### 运行测试
```bash
# Unity Test Runner
Window -> General -> Test Runner
```

### 测试覆盖
- 单元测试：核心组件功能测试
- 集成测试：SIP协议集成测试
- 性能测试：音视频延迟和资源使用测试

## 📊 性能指标

### 目标性能
- 🎵 音频延迟 < 150ms
- 📹 视频延迟 < 300ms
- 💾 内存使用 < 200MB
- ⚡ CPU使用率 < 30%
- 📞 通话成功率 > 95%

### 当前状态
- ✅ 音频通话基本功能正常
- 🔄 视频通话开发中
- ✅ 内存使用优化
- ✅ 错误处理完善

## 🔍 故障排除

### 常见问题

1. **FFmpeg未找到**
   ```
   解决方案：确保FFmpeg已安装并添加到系统PATH
   验证：ffmpeg -version
   ```

2. **SIP注册失败**
   ```
   解决方案：检查SIP服务器配置和网络连接
   验证：ping sip.server.com
   ```

3. **音频设备未找到**
   ```
   解决方案：检查麦克风和扬声器设备
   验证：Unity Audio Settings
   ```

4. **编译错误**
   ```
   解决方案：确保.NET Framework 4.8支持
   验证：Player Settings -> Configuration
   ```

## 📚 文档

详细的技术文档和重构方案请参考：
- [重构方案文档](./重构方案/README.md)
- [技术实现指南](./重构方案/02-技术实现方案.md)
- [核心代码实现](./重构方案/03-核心代码实现.md)

## 🤝 贡献

### 开发流程
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

### 代码规范
- 遵循C#编码规范
- 添加适当的注释和文档
- 编写单元测试
- 确保代码通过所有测试

## 📝 更新日志

### v2.0.0 (当前版本)
- 🔄 完全重构项目架构
- ✅ 集成SIPSorcery 8.0.6
- ✅ 添加SIPSorceryMedia.FFmpeg支持
- ✅ 实现新的音频端点
- ✅ 改进状态管理和错误处理

### v1.0.0 (旧版本)
- ✅ 基础SIP通话功能
- ⚠️ 存在架构和编解码器问题
- ❌ 已弃用

## 📞 支持

如有问题或建议，请：
1. 查看文档和故障排除指南
2. 检查已知问题列表
3. 创建Issue描述问题
4. 联系开发团队

## 📄 许可证

本项目采用MIT许可证，详情请参见LICENSE文件。

---

**基于SIPSorcery和FFmpeg的高质量Unity SIP客户端解决方案**
