﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>SIPSorceryMedia.Abstractions</id>
    <version>8.0.12</version>
    <title>SIPSorceryMedia.Abstractions</title>
    <authors><PERSON></authors>
    <license type="expression">BSD-3-Clause</license>
    <licenseUrl>https://licenses.nuget.org/BSD-3-Clause</licenseUrl>
    <icon>icon.png</icon>
    <readme>README.md</readme>
    <iconUrl>http://www.sipsorcery.com/mainsite/favicon.ico</iconUrl>
    <description>Don't reference this package unless you are building a media end point to work with the SIPSorcery real-time communications library. In most cases a concrete implementation package such as SIPSorceryMedia.Windows should be referenced.</description>
    <releaseNotes>-v8.0.12: Added IAudioEndPoint and GotEncodedMediaFrame to IAudioSink.
-v8.0.10: Change to text encoder interface.
-v8.0.7: New sampling frequencies and all sipsorcery packages release.
-v1.2.1: Expose LogFactory signature to generate generic logger. Added .net8.0 target
-v1.2.0: Add RawImage and new events / methods to avoid the use of byte[] to improve performance.
-v1.1.0: Stable release.
-v1.0.4-pre: Changed IAudioEncoder and IVideoEncoder to use SupportedFormats property instead of IsSupported method.
-v1.0.3-pre: Added video format to IVideoSink.GotVideoFrame. Removed 'V1' from namespace, the versioning mechanism is not going to be suitable for such a formative API.
-v1.0.2-pre: Improved pixel conversion routines to take a stride parameter and handle uneven dimensions.
-1.0.1: Added NV12 as a pixel format option.
-1.0.0: Initial stable release</releaseNotes>
    <copyright>Copyright © 2020-2025 Aaron Clauson</copyright>
    <tags>WebRTC VoIP SIPSorcery Media</tags>
    <repository type="git" url="https://github.com/sipsorcery/SIPSorceryMedia.Abstractions" branch="master" commit="fed5627ea4626495a1a3d7895433a5dd27898649" />
    <dependencies>
      <group targetFramework=".NETCoreApp3.1">
        <dependency id="Microsoft.Extensions.Logging.Abstractions" version="9.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net5.0">
        <dependency id="Microsoft.Extensions.Logging.Abstractions" version="9.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net8.0">
        <dependency id="Microsoft.Extensions.Logging.Abstractions" version="9.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Microsoft.Extensions.Logging.Abstractions" version="9.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="Microsoft.Extensions.Logging.Abstractions" version="9.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>