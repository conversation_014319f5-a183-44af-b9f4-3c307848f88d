using UnityEngine;
using System;

/// <summary>
/// 重构验证脚本
/// 用于验证重构后的组件是否正常工作
/// </summary>
public class RefactorValidation : MonoBehaviour
{
    [Header("Validation Settings")]
    [SerializeField] private bool runValidationOnStart = true;
    [SerializeField] private bool logDetailedResults = true;
    
    [Header("Test Results")]
    [SerializeField] private bool sipClientValidation = false;
    [SerializeField] private bool audioEndPointValidation = false;
    [SerializeField] private bool configValidation = false;
    [SerializeField] private bool uiPanelValidation = false;
    
    private void Start()
    {
        if (runValidationOnStart)
        {
            RunValidation();
        }
    }
    
    [ContextMenu("Run Validation")]
    public void RunValidation()
    {
        Debug.Log("=== 开始重构验证 ===");
        
        ValidateSIPClient();
        ValidateAudioEndPoint();
        ValidateConfig();
        ValidateUIPanel();
        ValidateDeprecatedComponents();
        
        Debug.Log("=== 重构验证完成 ===");
        LogValidationSummary();
    }
    
    private void ValidateSIPClient()
    {
        try
        {
            var sipClient = FindObjectOfType<UnitySIPClient>();
            if (sipClient != null)
            {
                sipClientValidation = true;
                if (logDetailedResults)
                    Debug.Log("✅ UnitySIPClient 组件验证通过");
            }
            else
            {
                sipClientValidation = false;
                Debug.LogWarning("⚠️ 场景中未找到 UnitySIPClient 组件");
            }
        }
        catch (Exception ex)
        {
            sipClientValidation = false;
            Debug.LogError($"❌ UnitySIPClient 验证失败: {ex.Message}");
        }
    }
    
    private void ValidateAudioEndPoint()
    {
        try
        {
            var audioEndPoint = FindObjectOfType<UnityAudioEndPoint>();
            if (audioEndPoint != null)
            {
                audioEndPointValidation = true;
                if (logDetailedResults)
                    Debug.Log("✅ UnityAudioEndPoint 组件验证通过");
            }
            else
            {
                audioEndPointValidation = false;
                Debug.LogWarning("⚠️ 场景中未找到 UnityAudioEndPoint 组件");
            }
        }
        catch (Exception ex)
        {
            audioEndPointValidation = false;
            Debug.LogError($"❌ UnityAudioEndPoint 验证失败: {ex.Message}");
        }
    }
    
    private void ValidateConfig()
    {
        try
        {
            var config = Resources.Load<SIPClientConfig>("DefaultSIPConfig");
            if (config != null)
            {
                configValidation = true;
                if (logDetailedResults)
                {
                    Debug.Log("✅ SIPClientConfig 配置验证通过");
                    Debug.Log($"   - 服务器: {config.ProxyServer}:{config.ProxyPort}");
                    Debug.Log($"   - 域名: {config.Domain}");
                    Debug.Log($"   - 自动注册: {config.AutoRegister}");
                }
            }
            else
            {
                configValidation = false;
                Debug.LogWarning("⚠️ 未找到 DefaultSIPConfig 资源文件");
            }
        }
        catch (Exception ex)
        {
            configValidation = false;
            Debug.LogError($"❌ SIPClientConfig 验证失败: {ex.Message}");
        }
    }
    
    private void ValidateUIPanel()
    {
        try
        {
            var uiPanel = FindObjectOfType<UISIPPanel>();
            if (uiPanel != null)
            {
                uiPanelValidation = true;
                if (logDetailedResults)
                    Debug.Log("✅ UISIPPanel 组件验证通过");
            }
            else
            {
                uiPanelValidation = false;
                Debug.LogWarning("⚠️ 场景中未找到 UISIPPanel 组件");
            }
        }
        catch (Exception ex)
        {
            uiPanelValidation = false;
            Debug.LogError($"❌ UISIPPanel 验证失败: {ex.Message}");
        }
    }
    
    private void ValidateDeprecatedComponents()
    {
        Debug.Log("--- 检查已弃用组件 ---");

        // 检查是否还有旧组件在使用（通过名称检查）
        var allComponents = FindObjectsOfType<MonoBehaviour>();

        bool foundDeprecatedAudioManager = false;
        bool foundDeprecatedVideoManager = false;
        bool foundDeprecatedUIManager = false;

        foreach (var component in allComponents)
        {
            string typeName = component.GetType().Name;

            if (typeName == "AudioManager" && typeName != "UnityAudioEndPoint")
            {
                foundDeprecatedAudioManager = true;
            }
            else if (typeName == "VideoManager" && typeName != "UnityVideoEndPoint")
            {
                foundDeprecatedVideoManager = true;
            }
            else if (typeName == "UIManager" && typeName != "UISIPPanel")
            {
                foundDeprecatedUIManager = true;
            }
        }

        if (foundDeprecatedAudioManager)
        {
            Debug.LogWarning("⚠️ 发现已弃用的 AudioManager 组件，建议替换为 UnityAudioEndPoint");
        }

        if (foundDeprecatedVideoManager)
        {
            Debug.LogWarning("⚠️ 发现已弃用的 VideoManager 组件，建议替换为 UnityVideoEndPoint");
        }

        if (foundDeprecatedUIManager)
        {
            Debug.LogWarning("⚠️ 发现已弃用的 UIManager 组件，建议替换为 UISIPPanel");
        }
        
        // 检查是否有G711.cs的引用
        if (logDetailedResults)
        {
            Debug.Log("✅ G711.cs 已删除，现在使用 SIPSorcery 内置编解码器");
        }
    }
    
    private void LogValidationSummary()
    {
        Debug.Log("--- 验证结果汇总 ---");
        Debug.Log($"UnitySIPClient: {(sipClientValidation ? "✅" : "❌")}");
        Debug.Log($"UnityAudioEndPoint: {(audioEndPointValidation ? "✅" : "❌")}");
        Debug.Log($"SIPClientConfig: {(configValidation ? "✅" : "❌")}");
        Debug.Log($"UISIPPanel: {(uiPanelValidation ? "✅" : "❌")}");
        
        int passedTests = 0;
        if (sipClientValidation) passedTests++;
        if (audioEndPointValidation) passedTests++;
        if (configValidation) passedTests++;
        if (uiPanelValidation) passedTests++;
        
        Debug.Log($"总体验证结果: {passedTests}/4 项通过");
        
        if (passedTests == 4)
        {
            Debug.Log("🎉 重构验证完全通过！项目已成功重构。");
        }
        else
        {
            Debug.LogWarning($"⚠️ 还有 {4 - passedTests} 项需要完善。");
        }
    }
    
    [ContextMenu("Check Dependencies")]
    public void CheckDependencies()
    {
        Debug.Log("=== 检查依赖包 ===");
        
        try
        {
            // 检查SIPSorcery是否可用
            var sipTransportType = Type.GetType("SIPSorcery.SIP.SIPTransport, SIPSorcery");
            if (sipTransportType != null)
            {
                Debug.Log("✅ SIPSorcery 库已正确加载");
            }
            else
            {
                Debug.LogError("❌ SIPSorcery 库未找到");
            }
            
            // 检查媒体抽象层
            var mediaEndPointsType = Type.GetType("SIPSorcery.Media.MediaEndPoints, SIPSorceryMedia.Abstractions");
            if (mediaEndPointsType != null)
            {
                Debug.Log("✅ SIPSorceryMedia.Abstractions 库已正确加载");
            }
            else
            {
                Debug.LogError("❌ SIPSorceryMedia.Abstractions 库未找到");
            }
            
        }
        catch (Exception ex)
        {
            Debug.LogError($"❌ 依赖检查失败: {ex.Message}");
        }
    }
    
    [ContextMenu("Test FFmpeg Availability")]
    public void TestFFmpegAvailability()
    {
        Debug.Log("=== 检查 FFmpeg 可用性 ===");
        
        try
        {
            var process = new System.Diagnostics.Process();
            process.StartInfo.FileName = "ffmpeg";
            process.StartInfo.Arguments = "-version";
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.RedirectStandardOutput = true;
            process.StartInfo.CreateNoWindow = true;
            
            process.Start();
            string output = process.StandardOutput.ReadToEnd();
            process.WaitForExit();
            
            if (process.ExitCode == 0 && output.Contains("ffmpeg version"))
            {
                Debug.Log("✅ FFmpeg 可用");
                if (logDetailedResults)
                {
                    var lines = output.Split('\n');
                    if (lines.Length > 0)
                        Debug.Log($"   版本: {lines[0]}");
                }
            }
            else
            {
                Debug.LogWarning("⚠️ FFmpeg 不可用或配置不正确");
            }
        }
        catch (Exception ex)
        {
            Debug.LogWarning($"⚠️ 无法检查 FFmpeg: {ex.Message}");
            Debug.Log("请确保 FFmpeg 已安装并添加到系统 PATH 环境变量中");
        }
    }
}
