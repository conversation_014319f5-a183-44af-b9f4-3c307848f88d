# Starting compiling UnityEngine.TestRunner.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-aab157f05391d2c43aa13bafd04c6478
# Starting compiling UnityEngine.UI.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-b0d2f8a62689d7048ad6521b9d8b067d
# Starting compiling UnityEditor.TestRunner.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-dbc5dfc7726156e4eac10856d4bbc8de
# Starting compiling UnityEditor.UI.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-9fe57612162af8a4eb42ccdb6a3a2fbc
# Starting compiling Unity.VSCode.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-618e5f369522d5549b541590c892a9c4
# Starting compiling Unity.VisualStudio.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-09f3c086643fc874781073fbc8543422
# Starting compiling Unity.Timeline.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-16fd3942aa7db3d408af26998773940e
# Starting compiling Unity.TextMeshPro.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-077f2129ad00c64418165ebe88225a04
# Starting compiling Unity.PlasticSCM.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-02c3b80dc28c355419fd1cd380c33a89
# Starting compiling Unity.Rider.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-29b5d04d1804931418492444c3552b24
# Starting compiling Unity.Timeline.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-12653d619f7f7a344b6eec037a08312f
# Starting compiling Unity.TextMeshPro.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-8a39e62eeeba0954abae5e461ca4b4de
# Starting compiling Unity.SysrootPackage.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-3a91cba44aa4f2f41badaf463ff244d7
# Starting compiling Unity.CollabProxy.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-e3a40329669134e4fbca91d602889c6b
# Starting compiling Unity.Sysroot.Linux_x86_64.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-5fcd13ed5bc769f4db2a77beaabe0ce0
# Starting compiling Unity.Toolchain.Win-x86_64-Linux-x86_64.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-580e818f5ec1e2942a6291b05b107072
# Starting compiling Assembly-CSharp.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-2649636c253baf141a1eec017756a6b2
