# Unity SIP客户端设置指南

## 🚀 快速设置步骤

### 1. 环境准备

#### 必需软件
- Unity 2020.3.48f1c1或更高版本
- FFmpeg命令行工具
- .NET Framework 4.8

#### FFmpeg安装验证
```bash
# 在命令行中运行以下命令验证FFmpeg安装
ffmpeg -version

# 应该看到类似输出：
# ffmpeg version 4.x.x Copyright (c) 2000-2023 the FFmpeg developers
```

### 2. Unity项目配置

#### Player Settings配置
1. 打开 `Edit -> Project Settings -> Player`
2. 设置以下选项：
   - **Scripting Runtime Version**: .NET Framework
   - **Api Compatibility Level**: .NET Framework
   - **Target Framework**: .NET Framework 4.8
   - **Allow Unsafe Code**: ✅ 启用

#### 音频设置
1. 打开 `Edit -> Project Settings -> Audio`
2. 确认以下设置：
   - **DSP Buffer Size**: 1024 samples (推荐)
   - **Sample Rate**: 48000 Hz
   - **Virtual Voice Count**: 512
   - **Real Voice Count**: 32

### 3. SIP配置

#### 创建SIP配置文件
1. 在Project窗口中右键点击
2. 选择 `Create -> SIP -> Client Config`
3. 命名为 `DefaultSIPConfig`
4. 配置以下参数：

```
SIP Server Settings:
├── Proxy Server: "sip.xbzt.cnpc"
├── Proxy Port: 5060
└── Domain: "xbzt.cnpc"

Authentication:
├── Username: "your_username"
├── Password: "your_password"
└── Display Name: "Your Display Name"

Registration Settings:
├── Auto Register: ✅
├── Expire Time: 3600 (seconds)
└── Registration Timeout: 10000 (ms)

Transport Settings:
├── Use UDP: ✅
├── Use TCP: ❌
└── Local IP: (留空自动检测)

Media Settings:
├── Enable Video: ✅
├── Audio Sample Rate: 48000
├── Video Width: 640
├── Video Height: 480
└── Video Frame Rate: 30
```

### 4. 场景设置

#### 创建SIP客户端GameObject
1. 在Hierarchy中创建空GameObject
2. 命名为 `SIPClient`
3. 添加以下组件：
   - `UnitySIPClient`
   - `UnityAudioEndPoint`
   - `UnityVideoEndPoint`

#### 配置UnitySIPClient
1. 将创建的SIPConfig拖拽到 `Sip Config` 字段
2. 将UnityAudioEndPoint拖拽到 `Audio End Point` 字段

#### 创建UI界面
1. 创建Canvas (如果没有)
2. 在Canvas下创建空GameObject，命名为 `SIPPanel`
3. 添加 `UISIPPanel` 组件
4. 创建以下UI元素：

```
SIPPanel
├── StatusText (Text组件)
├── RegistrationStatusText (Text组件)
├── StatusIndicator (Image组件)
├── DestinationInput (InputField组件)
├── CallButton (Button组件)
├── AnswerButton (Button组件)
├── HangupButton (Button组件)
├── RegisterButton (Button组件)
└── IncomingCallPanel (GameObject)
    └── IncomingCallerText (Text组件)
```

5. 将UI元素拖拽到UISIPPanel对应的字段中

### 5. 测试验证

#### 运行重构验证
1. 在SIPClient GameObject上添加 `RefactorValidation` 组件
2. 在Inspector中点击 `Run Validation` 按钮
3. 查看Console输出，确保所有验证项通过

#### 基础功能测试
1. 点击Play按钮启动项目
2. 检查Console是否有错误信息
3. 验证SIP注册状态
4. 测试音频设备检测

### 6. 常见问题解决

#### 编译错误
```
问题：CS0246: 找不到类型或命名空间名"SIPSorcery"
解决：确保NuGet包已正确导入，检查packages.config文件
```

#### 音频设备问题
```
问题：No microphone devices found
解决：检查系统音频设备，确保麦克风已连接并启用
```

#### SIP注册失败
```
问题：Registration failed: Connection timeout
解决：检查网络连接和SIP服务器配置
```

#### FFmpeg未找到
```
问题：FFmpeg not found in PATH
解决：下载FFmpeg并添加到系统PATH环境变量
```

### 7. 高级配置

#### 编解码器优先级
```csharp
// 在SIPClientConfig中设置
Preferred Audio Codec: OPUS (推荐) / G722 / G711
Preferred Video Codec: H264 (推荐) / VP8
```

#### 网络配置
```csharp
// 如果在NAT环境中，可能需要配置STUN服务器
// 这将在后续版本中支持
```

#### 性能优化
```csharp
// 音频缓冲区设置
Capture Interval: 0.02f (20ms，推荐)
Sample Rate: 48000 (高质量) / 16000 (低带宽)

// 视频设置
Frame Rate: 30fps (高质量) / 15fps (低带宽)
Resolution: 640x480 (推荐) / 320x240 (低带宽)
```

### 8. 开发调试

#### 启用详细日志
```csharp
// 在UnitySIPClient中启用调试模式
Debug.Log("详细的SIP协议日志");
```

#### 性能监控
```csharp
// 监控关键指标
- 音频延迟 < 150ms
- 视频延迟 < 300ms
- 内存使用 < 200MB
- CPU使用率 < 30%
```

### 9. 部署准备

#### 构建设置
1. 打开 `File -> Build Settings`
2. 选择目标平台 (Windows Standalone推荐)
3. 确保场景已添加到构建列表
4. 点击 `Build` 进行构建

#### 依赖文件
确保以下文件包含在构建中：
- SIPSorcery.dll
- SIPSorceryMedia.Abstractions.dll
- 其他NuGet依赖DLL文件

### 10. 技术支持

如遇到问题，请：
1. 查看Console错误信息
2. 运行RefactorValidation验证
3. 检查重构方案文档
4. 联系技术支持团队

---

**设置完成后，您就可以开始使用Unity SIP客户端进行音视频通话了！**
