<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator><PERSON></dc:creator>
  <dc:description>Don't reference this package unless you are building a media end point to work with the SIPSorcery real-time communications library. In most cases a concrete implementation package such as SIPSorceryMedia.Windows should be referenced.</dc:description>
  <dc:identifier>SIPSorceryMedia.Abstractions</dc:identifier>
  <version>8.0.12</version>
  <keywords>WebRTC VoIP SIPSorcery Media</keywords>
  <lastModifiedBy>NuGet.Build.Tasks.Pack, Version=********, Culture=neutral, PublicKeyToken=31bf3856ad364e35;.NET Standard 2.0</lastModifiedBy>
</coreProperties>