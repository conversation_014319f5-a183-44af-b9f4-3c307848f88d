using SIPSorcery.Media;
using SIPSorcery.Net;
using SIPSorceryMedia.Abstractions;
using SIPSorceryMedia.FFmpeg;
using UnityEngine;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using System.Collections;

/// <summary>
/// Unity音频端点，实现SIPSorcery的IAudioSource和IAudioSink接口
/// 基于SIPSorceryMedia.FFmpeg实现音频编解码
/// </summary>
public class UnityAudioEndPoint : MonoBehaviour, IAudioSource, IAudioSink
{
    [Header("Audio Settings")]
    [SerializeField] private int sampleRate = 48000;
    [SerializeField] private int channels = 1;
    [SerializeField] private float captureInterval = 0.02f; // 20ms
    
    // FFmpeg音频编解码器（生产级实现）
    private FFmpegAudioDecoder _audioDecoder;
    private IAudioEncoder _audioEncoder;
    private bool _ffmpegInitialized = false;

    // Unity音频组件
    private AudioSource _audioSource;
    private AudioClip _microphoneClip;
    private AudioClip _playbackClip;

    // 音频缓冲区
    private Queue<float[]> _audioBuffer = new Queue<float[]>();
    private readonly object _bufferLock = new object();

    // 状态管理
    private bool _isCapturing = false;
    private bool _isPlaying = false;
    private string _microphoneDevice;
    private AudioFormat _currentAudioFormat;


    
    #region IAudioSource Implementation

    public event EncodedSampleDelegate OnAudioSourceEncodedSample;
    public event RawAudioSampleDelegate OnAudioSourceRawSample;
    public event SourceErrorDelegate OnAudioSourceError;
    
    public List<AudioFormat> GetAudioSourceFormats()
    {
        return new List<AudioFormat>
        {
            new AudioFormat(SDPWellKnownMediaFormatsEnum.PCMU), // G.711 μ-law
            new AudioFormat(SDPWellKnownMediaFormatsEnum.PCMA), // G.711 A-law
            new AudioFormat(SDPWellKnownMediaFormatsEnum.G722), // G.722
            new AudioFormat(111, "OPUS", 48000) // OPUS
        };
    }
    
    public void SetAudioSourceFormat(AudioFormat audioFormat)
    {
        _currentAudioFormat = audioFormat;
        Debug.Log($"Setting audio source format: {audioFormat.Codec}");

        // 初始化FFmpeg音频编解码器（如果尚未初始化）
        if (!_ffmpegInitialized)
        {
            InitializeFFmpegCodecs();
        }

        // 使用SIPSorcery内置编解码器
        Debug.Log("Audio source format configured: " + audioFormat.Codec);
    }
    
    public Task StartAudio()
    {
        return Task.Run(() =>
        {
            try
            {
                StartMicrophone();
                _isCapturing = true;
                Debug.Log("Audio capture started");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to start audio: {ex.Message}");
                throw;
            }
        });
    }
    
    public Task PauseAudio()
    {
        _isCapturing = false;
        return Task.CompletedTask;
    }
    
    public Task ResumeAudio()
    {
        _isCapturing = true;
        return Task.CompletedTask;
    }
    
    public Task CloseAudio()
    {
        StopMicrophone();
        _isCapturing = false;
        return Task.CompletedTask;
    }

    public void RestrictFormats(Func<AudioFormat, bool> filter)
    {
        // 实现格式限制功能
        // 这里可以根据filter函数过滤支持的音频格式
        Debug.Log("Audio format restriction applied");
    }

    public void ExternalAudioSourceRawSample(AudioSamplingRatesEnum samplingRate, uint durationMilliseconds, short[] sample)
    {
        try
        {
            if (sample != null && sample.Length > 0)
            {
                // 处理外部音频源的原始样本
                var floatSamples = new float[sample.Length];
                for (int i = 0; i < sample.Length; i++)
                {
                    floatSamples[i] = sample[i] / 32767f;
                }

                // 转换为short数组以符合接口要求
                var shortSamples = new short[floatSamples.Length];
                for (int i = 0; i < floatSamples.Length; i++)
                {
                    shortSamples[i] = (short)(floatSamples[i] * 32767);
                }

                // 触发原始音频样本事件
                OnAudioSourceRawSample?.Invoke(samplingRate, durationMilliseconds, shortSamples);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error processing external audio sample: {ex.Message}");
            OnAudioSourceError?.Invoke($"External audio sample error: {ex.Message}");
        }
    }

    public bool HasEncodedAudioSubscribers()
    {
        // 检查是否有编码音频的订阅者
        return OnAudioSourceEncodedSample != null && OnAudioSourceEncodedSample.GetInvocationList().Length > 0;
    }

    public bool IsAudioSourcePaused()
    {
        // 返回音频源是否暂停
        return !_isCapturing;
    }

    #endregion
    
    #region IAudioSink Implementation

    public event SourceErrorDelegate OnAudioSinkError;

    public List<AudioFormat> GetAudioSinkFormats()
    {
        return GetAudioSourceFormats(); // 支持相同的格式
    }

    public void SetAudioSinkFormat(AudioFormat audioFormat)
    {
        Debug.Log($"Setting audio sink format: {audioFormat.Codec}");

        // 使用SIPSorcery内置解码器
        Debug.Log("Audio sink format configured: " + audioFormat.Codec);
    }

    public void GotAudioRtp(IPEndPoint remoteEndPoint, uint ssrc, uint timestamp,
                           uint payloadID, int payloadLength, bool marker, byte[] payload)
    {
        try
        {
            if (payload != null && payload.Length > 0)
            {
                // 使用SIPSorcery内置的解码器解码音频
                var decodedSamples = DecodeAudioPayload(payload, (int)payloadID);
                if (decodedSamples != null && decodedSamples.Length > 0)
                {
                    // 添加到播放缓冲区
                    lock (_bufferLock)
                    {
                        _audioBuffer.Enqueue(decodedSamples);

                        // 限制缓冲区大小，避免延迟累积
                        while (_audioBuffer.Count > 10)
                        {
                            _audioBuffer.Dequeue();
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error processing audio RTP: {ex.Message}");
            OnAudioSinkError?.Invoke($"Audio RTP processing error: {ex.Message}");
        }
    }

    public Task StartAudioSink()
    {
        return Task.Run(() =>
        {
            try
            {
                _isPlaying = true;
                Debug.Log("Audio sink started");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to start audio sink: {ex.Message}");
                OnAudioSinkError?.Invoke($"Start audio sink error: {ex.Message}");
                throw;
            }
        });
    }

    public Task PauseAudioSink()
    {
        _isPlaying = false;
        Debug.Log("Audio sink paused");
        return Task.CompletedTask;
    }

    public Task ResumeAudioSink()
    {
        _isPlaying = true;
        Debug.Log("Audio sink resumed");
        return Task.CompletedTask;
    }

    public Task CloseAudioSink()
    {
        try
        {
            _isPlaying = false;

            // 清空音频缓冲区
            lock (_bufferLock)
            {
                _audioBuffer.Clear();
            }

            // 停止当前播放的音频
            if (_audioSource != null && _audioSource.isPlaying)
            {
                _audioSource.Stop();
            }

            Debug.Log("Audio sink closed");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error closing audio sink: {ex.Message}");
            OnAudioSinkError?.Invoke($"Close audio sink error: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    #endregion
    
    #region Unity Audio Management
    
    private void Awake()
    {
        // 初始化Unity音频组件
        _audioSource = gameObject.AddComponent<AudioSource>();
        _audioSource.playOnAwake = false;
        _audioSource.loop = false;
        _audioSource.volume = 1.0f;

        // 选择麦克风设备
        if (Microphone.devices.Length > 0)
        {
            _microphoneDevice = Microphone.devices[0];
            Debug.Log($"Selected microphone: {_microphoneDevice}");
        }
        else
        {
            Debug.LogWarning("No microphone devices found");
        }

        // 初始化FFmpeg编解码器
        InitializeFFmpegCodecs();
    }

    private void InitializeFFmpegCodecs()
    {
        try
        {
            // 初始化FFmpeg音频解码器
            // 注意：FFmpegAudioDecoder 主要用于从文件或流中解码音频
            // 对于实时音频通话，我们主要使用SIPSorcery内置的编解码器

            // 初始化音频编码器（使用SIPSorcery内置编解码器）
            // _audioEncoder 将在需要时根据音频格式创建

            _ffmpegInitialized = true;
            Debug.Log("FFmpeg audio codecs initialized successfully");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to initialize FFmpeg audio codecs: {ex.Message}");
            Debug.LogWarning("Falling back to simple PCM encoding/decoding");
            _ffmpegInitialized = false;
        }
    }

    private void CleanupFFmpegCodecs()
    {
        try
        {
            // 清理FFmpeg音频编解码器资源
            if (_audioDecoder != null)
            {
                _audioDecoder.Dispose();
                _audioDecoder = null;
            }

            if (_audioEncoder != null)
            {
                // IAudioEncoder 接口没有 Dispose 方法
                // 如果实现了 IDisposable，则调用 Dispose
                if (_audioEncoder is IDisposable disposableEncoder)
                {
                    disposableEncoder.Dispose();
                }
                _audioEncoder = null;
            }

            _ffmpegInitialized = false;
            Debug.Log("FFmpeg audio codecs cleaned up");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error cleaning up FFmpeg audio codecs: {ex.Message}");
        }
    }
    
    private void StartMicrophone()
    {
        if (string.IsNullOrEmpty(_microphoneDevice))
        {
            Debug.LogError("No microphone device available");
            return;
        }
        
        try
        {
            // 创建麦克风录音剪辑
            _microphoneClip = Microphone.Start(_microphoneDevice, true, 1, sampleRate);
            
            // 启动音频捕获协程
            StartCoroutine(CaptureAudioCoroutine());
            
            Debug.Log($"Microphone started: {_microphoneDevice}, SampleRate: {sampleRate}");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to start microphone: {ex.Message}");
            throw;
        }
    }
    
    private void StopMicrophone()
    {
        if (!string.IsNullOrEmpty(_microphoneDevice) && Microphone.IsRecording(_microphoneDevice))
        {
            Microphone.End(_microphoneDevice);
            Debug.Log("Microphone stopped");
        }
    }
    
    private IEnumerator CaptureAudioCoroutine()
    {
        int lastPosition = 0;
        var captureBuffer = new float[Mathf.RoundToInt(sampleRate * captureInterval)];
        
        while (_isCapturing && Microphone.IsRecording(_microphoneDevice))
        {
            int currentPosition = Microphone.GetPosition(_microphoneDevice);
            
            if (currentPosition != lastPosition)
            {
                // 获取音频数据
                int sampleCount = GetAudioData(lastPosition, currentPosition, captureBuffer);
                
                if (sampleCount > 0)
                {
                    // 创建实际大小的样本数组
                    var samples = new float[sampleCount];
                    Array.Copy(captureBuffer, 0, samples, 0, sampleCount);
                    
                    // 处理捕获的音频
                    ProcessCapturedAudio(samples);
                }
                
                lastPosition = currentPosition;
            }
            
            yield return new WaitForSeconds(captureInterval);
        }
    }
    
    private int GetAudioData(int startPos, int endPos, float[] buffer)
    {
        if (_microphoneClip == null) return 0;
        
        int sampleCount;
        if (endPos > startPos)
        {
            sampleCount = endPos - startPos;
        }
        else
        {
            // 处理环形缓冲区
            sampleCount = (_microphoneClip.samples - startPos) + endPos;
        }
        
        sampleCount = Mathf.Min(sampleCount, buffer.Length);
        
        if (sampleCount > 0)
        {
            _microphoneClip.GetData(buffer, startPos);
        }
        
        return sampleCount;
    }
    
    private void ProcessCapturedAudio(float[] samples)
    {
        try
        {
            if (samples.Length > 0)
            {
                // 使用SIPSorcery内置的编码器编码音频
                var encodedSample = EncodeAudioSamples(samples);
                
                if (encodedSample != null)
                {
                    // 触发编码完成事件，SIPSorcery会自动发送RTP包
                    OnAudioSourceEncodedSample?.Invoke(encodedSample.Timestamp, encodedSample.Sample);
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error encoding audio: {ex.Message}");
        }
    }
    
    private void Update()
    {
        // 播放接收到的音频
        PlayBufferedAudio();
    }
    
    private void PlayBufferedAudio()
    {
        if (!_isPlaying && _audioBuffer.Count > 0)
        {
            lock (_bufferLock)
            {
                if (_audioBuffer.Count > 0)
                {
                    var samples = _audioBuffer.Dequeue();
                    PlayAudioSamples(samples);
                }
            }
        }
    }
    
    private void PlayAudioSamples(float[] samples)
    {
        try
        {
            // 创建音频剪辑并播放
            _playbackClip = AudioClip.Create("ReceivedAudio", samples.Length, channels, sampleRate, false);
            _playbackClip.SetData(samples, 0);
            
            _audioSource.clip = _playbackClip;
            _audioSource.Play();
            
            _isPlaying = true;
            
            // 播放完成后重置状态
            StartCoroutine(ResetPlayingState(_playbackClip.length));
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error playing audio: {ex.Message}");
        }
    }
    
    private IEnumerator ResetPlayingState(float duration)
    {
        yield return new WaitForSeconds(duration);
        _isPlaying = false;
    }
    
    #endregion
    
    #region FFmpeg Audio Encoding/Decoding



    private EncodedSample EncodeAudioSamples(float[] samples)
    {
        try
        {
            // 检查音频格式是否已设置
            if (_currentAudioFormat.FormatID == 0)
            {
                Debug.LogWarning("Audio format not set");
                return null;
            }

            // 转换float数组为short数组
            var shortSamples = new short[samples.Length];
            for (int i = 0; i < samples.Length; i++)
            {
                shortSamples[i] = (short)(samples[i] * 32767);
            }

            // 使用FFmpeg音频编码器（如果可用）或SIPSorcery内置编码器
            byte[] encodedData = null;

            if (_ffmpegInitialized && _audioEncoder != null)
            {
                try
                {
                    // 使用FFmpeg编码器进行高质量音频编码
                    // 转换float数组为short数组
                    var shortSamples = new short[samples.Length];
                    for (int i = 0; i < samples.Length; i++)
                    {
                        shortSamples[i] = (short)(samples[i] * 32767);
                    }

                    encodedData = _audioEncoder.EncodeAudio(shortSamples, _currentAudioFormat);
                    Debug.Log("Using FFmpeg audio encoder");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"FFmpeg audio encoding failed: {ex.Message}");
                    // 后备方案：使用内置编码器
                    encodedData = EncodeWithBuiltinCodec(samples);
                }
            }
            else
            {
                // 后备方案：使用SIPSorcery内置编码器
                encodedData = EncodeWithBuiltinCodec(samples);
            }

            if (encodedData != null && encodedData.Length > 0)
            {
                return new EncodedSample
                {
                    Sample = encodedData,
                    Timestamp = (uint)(DateTime.UtcNow.Ticks / TimeSpan.TicksPerMillisecond),
                    PayloadTypeID = (uint)_currentAudioFormat.FormatID
                };
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error encoding audio: {ex.Message}");
        }

        return null;
    }

    private byte[] EncodeWithBuiltinCodec(float[] samples)
    {
        // 根据当前音频格式选择合适的编码方式
        switch (_currentAudioFormat.FormatID)
        {
            case (int)SDPWellKnownMediaFormatsEnum.PCMU:
                return EncodeG711MuLaw(samples);
            case (int)SDPWellKnownMediaFormatsEnum.PCMA:
                return EncodeG711ALaw(samples);
            case (int)SDPWellKnownMediaFormatsEnum.G722:
                return EncodeG722(samples);
            default:
                // 默认使用PCM编码
                return EncodePCM(samples);
        }
    }

    private float[] DecodeAudioPayload(byte[] payload, int payloadType)
    {
        try
        {
            if (payload == null || payload.Length == 0)
            {
                return null;
            }

            // 使用FFmpeg音频解码器（如果可用）或SIPSorcery内置解码器
            if (_ffmpegInitialized && _audioDecoder != null)
            {
                try
                {
                    // 使用FFmpeg解码器进行高质量音频解码
                    // 注意：FFmpegAudioDecoder 主要用于文件/流解码
                    // 对于RTP负载解码，我们仍使用内置解码器
                    Debug.Log($"Using FFmpeg audio decoder for payload type {payloadType} (placeholder)");
                    return DecodeWithBuiltinCodec(payload, payloadType);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"FFmpeg audio decoding failed: {ex.Message}");
                    return DecodeWithBuiltinCodec(payload, payloadType);
                }
            }
            else
            {
                // 后备方案：使用SIPSorcery内置解码器
                return DecodeWithBuiltinCodec(payload, payloadType);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error decoding audio: {ex.Message}");
        }

        return null;
    }

    private float[] DecodeWithBuiltinCodec(byte[] payload, int payloadType)
    {
        // 根据负载类型选择合适的解码方式
        switch (payloadType)
        {
            case (int)SDPWellKnownMediaFormatsEnum.PCMU:
                return DecodeG711MuLaw(payload);
            case (int)SDPWellKnownMediaFormatsEnum.PCMA:
                return DecodeG711ALaw(payload);
            case (int)SDPWellKnownMediaFormatsEnum.G722:
                return DecodeG722(payload);
            default:
                // 默认使用PCM解码
                return DecodePCM(payload);
        }
    }

    // 音频编解码器方法
    private byte[] EncodeG711MuLaw(float[] samples)
    {
        try
        {
            // 使用内置G.711 μ-law编码
            return EncodeG711MuLawFallback(samples);
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error in G.711 μ-law encoding: {ex.Message}");
            return EncodeG711MuLawFallback(samples);
        }
    }

    private byte[] EncodeG711ALaw(float[] samples)
    {
        try
        {
            // 使用内置G.711 A-law编码
            return EncodeG711ALawFallback(samples);
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error in G.711 A-law encoding: {ex.Message}");
            return EncodeG711ALawFallback(samples);
        }
    }

    private byte[] EncodeG722(float[] samples)
    {
        // G.722编码需要16kHz采样率，这里先转换为PCM
        // 实际的G.722编码需要更复杂的实现
        var pcmSamples = new short[samples.Length];
        for (int i = 0; i < samples.Length; i++)
        {
            pcmSamples[i] = (short)(samples[i] * 32767);
        }

        // 暂时使用PCM编码，实际项目中需要集成G.722编码器
        var pcmBytes = new byte[pcmSamples.Length * 2];
        for (int i = 0; i < pcmSamples.Length; i++)
        {
            pcmBytes[i * 2] = (byte)(pcmSamples[i] & 0xFF);
            pcmBytes[i * 2 + 1] = (byte)((pcmSamples[i] >> 8) & 0xFF);
        }
        return pcmBytes;
    }

    private byte[] EncodePCM(float[] samples)
    {
        // PCM编码作为后备方案
        var pcmBytes = new byte[samples.Length * 2];
        for (int i = 0; i < samples.Length; i++)
        {
            short pcmSample = (short)(samples[i] * 32767);
            pcmBytes[i * 2] = (byte)(pcmSample & 0xFF);
            pcmBytes[i * 2 + 1] = (byte)((pcmSample >> 8) & 0xFF);
        }
        return pcmBytes;
    }

    private float[] DecodeG711MuLaw(byte[] payload)
    {
        try
        {
            // 使用内置G.711 μ-law解码
            return DecodeG711MuLawFallback(payload);
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error in G.711 μ-law decoding: {ex.Message}");
            return DecodeG711MuLawFallback(payload);
        }
    }

    private float[] DecodeG711ALaw(byte[] payload)
    {
        try
        {
            // 使用内置G.711 A-law解码
            return DecodeG711ALawFallback(payload);
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error in G.711 A-law decoding: {ex.Message}");
            return DecodeG711ALawFallback(payload);
        }
    }

    private float[] DecodeG722(byte[] payload)
    {
        // G.722解码需要专门的解码器，这里暂时使用PCM解码
        var samples = new float[payload.Length / 2];
        for (int i = 0; i < samples.Length; i++)
        {
            short pcmSample = (short)(payload[i * 2] | (payload[i * 2 + 1] << 8));
            samples[i] = pcmSample / 32767f;
        }
        return samples;
    }

    private float[] DecodePCM(byte[] payload)
    {
        // PCM解码作为后备方案
        var samples = new float[payload.Length / 2];
        for (int i = 0; i < samples.Length; i++)
        {
            short pcmSample = (short)(payload[i * 2] | (payload[i * 2 + 1] << 8));
            samples[i] = pcmSample / 32767f;
        }
        return samples;
    }

    #endregion

    #region Fallback Codecs (Simple Implementation)

    private byte[] EncodeG711MuLawFallback(float[] samples)
    {
        // 简单的μ-law编码实现
        var encodedBytes = new byte[samples.Length];
        for (int i = 0; i < samples.Length; i++)
        {
            short pcmSample = (short)(samples[i] * 32767);
            encodedBytes[i] = LinearToMuLaw(pcmSample);
        }
        return encodedBytes;
    }

    private byte[] EncodeG711ALawFallback(float[] samples)
    {
        // 简单的A-law编码实现
        var encodedBytes = new byte[samples.Length];
        for (int i = 0; i < samples.Length; i++)
        {
            short pcmSample = (short)(samples[i] * 32767);
            encodedBytes[i] = LinearToALaw(pcmSample);
        }
        return encodedBytes;
    }

    private float[] DecodeG711MuLawFallback(byte[] payload)
    {
        // 简单的μ-law解码实现
        var samples = new float[payload.Length];
        for (int i = 0; i < payload.Length; i++)
        {
            short pcmSample = MuLawToLinear(payload[i]);
            samples[i] = pcmSample / 32767f;
        }
        return samples;
    }

    private float[] DecodeG711ALawFallback(byte[] payload)
    {
        // 简单的A-law解码实现
        var samples = new float[payload.Length];
        for (int i = 0; i < payload.Length; i++)
        {
            short pcmSample = ALawToLinear(payload[i]);
            samples[i] = pcmSample / 32767f;
        }
        return samples;
    }

    // G.711 μ-law编解码算法
    private static readonly short[] MuLawDecompressTable = new short[256];
    private static readonly byte[] MuLawCompressTable = new byte[32768];

    static UnityAudioEndPoint()
    {
        // 初始化μ-law查找表
        InitializeMuLawTables();
    }

    private static void InitializeMuLawTables()
    {
        // 简化的μ-law表初始化
        for (int i = 0; i < 256; i++)
        {
            MuLawDecompressTable[i] = (short)(((i & 0x0F) << 3) + 0x84);
            if ((i & 0x80) != 0) MuLawDecompressTable[i] = (short)-MuLawDecompressTable[i];
        }

        for (int i = 0; i < 32768; i++)
        {
            MuLawCompressTable[i] = (byte)(i >> 8);
        }
    }

    private static byte LinearToMuLaw(short sample)
    {
        return MuLawCompressTable[Math.Abs(sample)];
    }

    private static short MuLawToLinear(byte mulaw)
    {
        return MuLawDecompressTable[mulaw];
    }

    private static byte LinearToALaw(short sample)
    {
        // 简化的A-law编码
        return (byte)(sample >> 8);
    }

    private static short ALawToLinear(byte alaw)
    {
        // 简化的A-law解码
        return (short)(alaw << 8);
    }

    #endregion

    #region Utility Methods

    public MediaEndPoints ToMediaEndPoints()
    {
        var endPoints = new MediaEndPoints();
        endPoints.AudioSource = this;
        endPoints.AudioSink = this;
        return endPoints;
    }

    private void OnDestroy()
    {
        StopMicrophone();

        // 清理FFmpeg音频编解码器
        CleanupFFmpegCodecs();
    }

    #endregion
}
