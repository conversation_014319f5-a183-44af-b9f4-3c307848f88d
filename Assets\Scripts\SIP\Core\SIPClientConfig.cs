using UnityEngine;

/// <summary>
/// SIP客户端配置
/// </summary>
[CreateAssetMenu(fileName = "SIPClientConfig", menuName = "SIP/Client Config")]
public class SIPClientConfig : ScriptableObject
{
    [Header("SIP Server Settings")]
    [Tooltip("SIP代理服务器地址")]
    public string ProxyServer = "sip.xbzt.cnpc";
    
    [Tooltip("SIP代理服务器端口")]
    public int ProxyPort = 5060;
    
    [Tooltip("SIP域名")]
    public string Domain = "xbzt.cnpc";
    
    [Header("Authentication")]
    [Tooltip("SIP用户名")]
    public string Username = "";
    
    [Tooltip("SIP密码")]
    public string Password = "";
    
    [Tooltip("显示名称")]
    public string DisplayName = "";
    
    [Header("Registration Settings")]
    [Tooltip("是否自动注册")]
    public bool AutoRegister = true;

    [Tooltip("注册过期时间（秒）")]
    public int ExpireTime = 3600;

    [Toolt<PERSON>("注册超时时间（毫秒）")]
    public int RegistrationTimeout = 10000;

    [Header("Call Settings")]
    [Tooltip("是否自动接听来电")]
    public bool AutoAnswer = false;
    
    [Header("Transport Settings")]
    [Tooltip("使用UDP传输")]
    public bool UseUDP = true;
    
    [Tooltip("使用TCP传输")]
    public bool UseTCP = false;
    
    [Tooltip("本地IP地址（留空自动检测）")]
    public string LocalIP = "";
    
    [Header("Media Settings")]
    [Tooltip("启用视频通话")]
    public bool EnableVideo = true;
    
    [Tooltip("音频采样率")]
    public int AudioSampleRate = 48000;
    
    [Tooltip("视频分辨率宽度")]
    public int VideoWidth = 640;
    
    [Tooltip("视频分辨率高度")]
    public int VideoHeight = 480;
    
    [Tooltip("视频帧率")]
    public int VideoFrameRate = 30;
    
    [Header("Codec Preferences")]
    [Tooltip("首选音频编解码器")]
    public AudioCodecPreference PreferredAudioCodec = AudioCodecPreference.OPUS;
    
    [Tooltip("首选视频编解码器")]
    public VideoCodecPreference PreferredVideoCodec = VideoCodecPreference.H264;
}

public enum AudioCodecPreference
{
    G711,
    G722,
    OPUS
}

public enum VideoCodecPreference
{
    H264,
    VP8
}
