{"Version": 1, "WorkspaceRootPath": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Assets\\Scripts\\SIPClienta.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\Scripts\\SIPClienta.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\3b97c1fc96cf2ca46443670794c0413b2dc3b4e06f21cffbc3eb4225973922e1\\SIPRegistrationUserAgent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B3F03361-B52A-AA85-7A4B-33E69644EFE1}|Unity.Rider.Editor.csproj|D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\library\\packagecache\\com.unity.ide.rider@3.0.21\\rider\\editor\\projectgeneration\\assemblynameprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B3F03361-B52A-AA85-7A4B-33E69644EFE1}|Unity.Rider.Editor.csproj|solutionrelative:library\\packagecache\\com.unity.ide.rider@3.0.21\\rider\\editor\\projectgeneration\\assemblynameprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BCB74BFF-78C1-3636-698C-1C875C74E836}|UnityEngine.TestRunner.csproj|D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\library\\packagecache\\com.unity.test-framework@1.1.33\\unityengine.testrunner\\nunitextensions\\runner\\compositeworkitem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BCB74BFF-78C1-3636-698C-1C875C74E836}|UnityEngine.TestRunner.csproj|solutionrelative:library\\packagecache\\com.unity.test-framework@1.1.33\\unityengine.testrunner\\nunitextensions\\runner\\compositeworkitem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{30157B2F-B5B1-B00C-CFF6-B8F2F401DD60}|UnityEditor.TestRunner.csproj|D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\library\\packagecache\\com.unity.test-framework@1.1.33\\unityeditor.testrunner\\testlaunchers\\attributefinderbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{30157B2F-B5B1-B00C-CFF6-B8F2F401DD60}|UnityEditor.TestRunner.csproj|solutionrelative:library\\packagecache\\com.unity.test-framework@1.1.33\\unityeditor.testrunner\\testlaunchers\\attributefinderbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Assets\\Scripts\\SIPConfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\Scripts\\SIPConfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Assets\\Scripts\\AudioManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\Scripts\\AudioManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "SIPRegistrationUserAgent.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\3b97c1fc96cf2ca46443670794c0413b2dc3b4e06f21cffbc3eb4225973922e1\\SIPRegistrationUserAgent.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\3b97c1fc96cf2ca46443670794c0413b2dc3b4e06f21cffbc3eb4225973922e1\\SIPRegistrationUserAgent.cs", "ViewState": "AgIAALEAAAAAAAAAAAAAALEAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-26T08:27:24.13Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "AssemblyNameProvider.cs", "DocumentMoniker": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Library\\PackageCache\\com.unity.ide.rider@3.0.21\\Rider\\Editor\\ProjectGeneration\\AssemblyNameProvider.cs", "RelativeDocumentMoniker": "Library\\PackageCache\\com.unity.ide.rider@3.0.21\\Rider\\Editor\\ProjectGeneration\\AssemblyNameProvider.cs", "ToolTip": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Library\\PackageCache\\com.unity.ide.rider@3.0.21\\Rider\\Editor\\ProjectGeneration\\AssemblyNameProvider.cs", "RelativeToolTip": "Library\\PackageCache\\com.unity.ide.rider@3.0.21\\Rider\\Editor\\ProjectGeneration\\AssemblyNameProvider.cs", "ViewState": "AgIAADsAAAAAAAAAAAAAADsAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T10:48:29.51Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "CompositeWorkItem.cs", "DocumentMoniker": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs", "RelativeDocumentMoniker": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs", "ToolTip": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs", "RelativeToolTip": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs", "ViewState": "AgIAALwAAAAAAAAAAAAIwMkAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T10:36:57.993Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "AttributeFinderBase.cs", "DocumentMoniker": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\AttributeFinderBase.cs", "RelativeDocumentMoniker": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\AttributeFinderBase.cs", "ToolTip": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\AttributeFinderBase.cs", "RelativeToolTip": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\AttributeFinderBase.cs", "ViewState": "AgIAAEkAAAAAAAAAAAAuwFgAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T10:36:53.847Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "SIPClienta.cs", "DocumentMoniker": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Assets\\Scripts\\SIPClienta.cs", "RelativeDocumentMoniker": "Assets\\Scripts\\SIPClienta.cs", "ToolTip": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Assets\\Scripts\\SIPClienta.cs", "RelativeToolTip": "Assets\\Scripts\\SIPClienta.cs", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T10:34:50.899Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "SIPConfig.cs", "DocumentMoniker": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Assets\\Scripts\\SIPConfig.cs", "RelativeDocumentMoniker": "Assets\\Scripts\\SIPConfig.cs", "ToolTip": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Assets\\Scripts\\SIPConfig.cs", "RelativeToolTip": "Assets\\Scripts\\SIPConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T09:59:07.818Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "AudioManager.cs", "DocumentMoniker": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Assets\\Scripts\\AudioManager.cs", "RelativeDocumentMoniker": "Assets\\Scripts\\AudioManager.cs", "ToolTip": "D:\\2024-5-15\\UnityProject\\SIPSorcerytest4.2\\Assets\\Scripts\\AudioManager.cs", "RelativeToolTip": "Assets\\Scripts\\AudioManager.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAQwDkAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-06T10:11:21.599Z"}]}]}]}