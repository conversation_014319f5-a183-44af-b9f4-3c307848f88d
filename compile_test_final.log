[Licensing::Mo<PERSON><PERSON>] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to the License Client on channel: "LicenseClient-Administrator"
[Licensing::Client] Error: Code 10 while verifying Licensing Client signature (process Id: 12364, path: "D:/Unity/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] Error: LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.16.1+e780b2e
  Session Id:              c4666de4b5fb458f9a8f7c2784256980
  Correlation Id:          1fbcd0988cf76b75376ba4d50891216a
  External correlation Id: 225112887603918094
  Machine Id:              U3/kujaBavxMtf0Nl5L1Up+1Qws=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-Administrator" (connect: 0.00s, validation: 0.01s, handshake: 0.26s)
[Licensing::IpcConnector] Successfully connected to the License Notification on channel: "LicenseClient-Administrator-notifications"
Entitlement-based licensing initiated
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully updated license
Built from '2020.3/china_unity/release' branch; Version is '2020.3.48f1c1 (06fbdfbf16e3) revision 457695'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 16290 MB
[Licensing::Client] Successfully resolved entitlements
[Licensing::Module] Serial number assigned to: "F4-JKH8-TABK-2XYH-8Q7K-XXXX"
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0
[Package Manager] Server::Start -- Port 7309 was selected

COMMAND LINE ARGUMENTS:
D:\Unity\2020.3.48f1c1\Editor\Unity.exe
-batchmode
-quit
-projectPath
D:\2024-5-15\UnityProject\SIPSorcerytest4.2
-executeMethod
UnityEditor.EditorApplication.Exit
-logFile
compile_test_final.log
Successfully changed project path to: D:\2024-5-15\UnityProject\SIPSorcerytest4.2
D:/2024-5-15/UnityProject/SIPSorcerytest4.2
Using Asset Import Pipeline V2.
Player connection [19932] Host "[IP] ************* [Port] 55496 [Flags] 2 [Guid] 3088213109 [EditorId] 3088213109 [Version] 1048832 [Id] WindowsEditor(7,xxzx-bzhzy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [19932] Host "[IP] ************* [Port] 55496 [Flags] 2 [Guid] 3088213109 [EditorId] 3088213109 [Version] 1048832 [Id] WindowsEditor(7,xxzx-bzhzy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.[Licensing::Client] Successfully resolved entitlements
[Package Manager] Restoring resolved packages state from cache
[Licensing::Client] Successfully resolved entitlement details
[Package Manager] Done checking package constraints in 0.00s seconds
[Package Manager] 
Registered 46 packages:
  Packages from [https://packages.unity.cn]:
    com.unity.collab-proxy@2.0.4 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.collab-proxy@2.0.4)
    com.unity.ide.rider@3.0.21 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.ide.rider@3.0.21)
    com.unity.ide.visualstudio@2.0.18 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.ide.visualstudio@2.0.18)
    com.unity.ide.vscode@1.2.5 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.ide.vscode@1.2.5)
    com.unity.test-framework@1.1.33 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33)
    com.unity.textmeshpro@3.0.6 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.textmeshpro@3.0.6)
    com.unity.timeline@1.4.8 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.timeline@1.4.8)
    com.unity.toolchain.win-x86_64-linux-x86_64@2.0.10 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.toolchain.win-x86_64-linux-x86_64@2.0.10)
    com.unity.sysroot@2.0.10 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.sysroot@2.0.10)
    com.unity.sysroot.linux-x86_64@2.0.9 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.sysroot.linux-x86_64@2.0.9)
    com.unity.ext.nunit@1.0.6 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.ext.nunit@1.0.6)
  Built-in packages:
    com.unity.ugui@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.ugui@1.0.0)
    com.unity.modules.ai@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.ai@1.0.0)
    com.unity.modules.androidjni@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.androidjni@1.0.0)
    com.unity.modules.animation@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.animation@1.0.0)
    com.unity.modules.assetbundle@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.assetbundle@1.0.0)
    com.unity.modules.audio@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.audio@1.0.0)
    com.unity.modules.autostreaming@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.autostreaming@1.0.0)
    com.unity.modules.cloth@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.cloth@1.0.0)
    com.unity.modules.director@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.director@1.0.0)
    com.unity.modules.imageconversion@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.imageconversion@1.0.0)
    com.unity.modules.imgui@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.imgui@1.0.0)
    com.unity.modules.jsonserialize@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.jsonserialize@1.0.0)
    com.unity.modules.particlesystem@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.particlesystem@1.0.0)
    com.unity.modules.physics@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.physics@1.0.0)
    com.unity.modules.physics2d@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.physics2d@1.0.0)
    com.unity.modules.screencapture@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.screencapture@1.0.0)
    com.unity.modules.terrain@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.terrain@1.0.0)
    com.unity.modules.terrainphysics@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.terrainphysics@1.0.0)
    com.unity.modules.tilemap@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.tilemap@1.0.0)
    com.unity.modules.ui@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.ui@1.0.0)
    com.unity.modules.uielements@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.uielements@1.0.0)
    com.unity.modules.umbra@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.umbra@1.0.0)
    com.unity.modules.unityanalytics@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.unityanalytics@1.0.0)
    com.unity.modules.unitywebrequest@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.unitywebrequest@1.0.0)
    com.unity.modules.unitywebrequestassetbundle@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.unitywebrequestassetbundle@1.0.0)
    com.unity.modules.unitywebrequestaudio@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.unitywebrequestaudio@1.0.0)
    com.unity.modules.unitywebrequesttexture@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.unitywebrequesttexture@1.0.0)
    com.unity.modules.unitywebrequestwww@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.unitywebrequestwww@1.0.0)
    com.unity.modules.vehicles@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.vehicles@1.0.0)
    com.unity.modules.video@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.video@1.0.0)
    com.unity.modules.vr@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.vr@1.0.0)
    com.unity.modules.wind@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.wind@1.0.0)
    com.unity.modules.xr@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.xr@1.0.0)
    com.unity.modules.subsystems@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.subsystems@1.0.0)
    com.unity.modules.uielementsnative@1.0.0 (location: D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.modules.uielementsnative@1.0.0)

[Subsystems] No new subsystems found in resolved package list.
Package Manager log level set to [2]
[Package Manager] Done registering packages in 1.68s seconds
Refreshing native plugins compatible for Editor in 438.42 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.48f1c1 (06fbdfbf16e3)
[Subsystems] Discovering subsystems at path D:/Unity/2020.3.48f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/2024-5-15/UnityProject/SIPSorcerytest4.2/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA GeForce GT 730 (ID=0x1287)
    Vendor:   
    VRAM:     2007 MB
    Driver:   30.0.14.7514
[Licensing::Client] Successfully resolved entitlements
Initialize mono
Mono path[0] = 'D:/Unity/2020.3.48f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56156
AcceleratorClientConnectionCallback - disconnected - :0
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.006110 seconds.
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 32.43 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.215 seconds
Domain Reload Profiling:
	ReloadAssembly (1216ms)
		BeginReloadAssembly (343ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (751ms)
			LoadAssemblies (379ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (200ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (44ms)
			SetupLoadedEditorAssemblies (325ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (33ms)
				BeforeProcessingInitializeOnLoad (22ms)
				ProcessInitializeOnLoadAttributes (118ms)
				ProcessInitializeOnLoadMethodAttributes (140ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Validating Project structure ... 0.000669 seconds.
[Licensing::Client] Successfully resolved entitlements

LICENSE SYSTEM [202578 18:25:47] Next license update check is after 2025-07-09T10:25:35

Shader import version has changed; will reimport all shaders...
Upgrading shader files ...0.000684 seconds.
Application.AssetDatabase Initial Script Refresh Start
Registering precompiled user dll's ...
Registered in 0.289117 seconds.
Start importing Assets/Scripts/SIP/Media/UnityVideoEndPoint.cs using Guid(e67e98455beb6d04891a3dcc72d420e5) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'b1bf4251c690347422297f1484b538ca') in 0.020069 seconds 
Start importing Assets/Scripts/SIP/Media/UnityAudioEndPoint.cs using Guid(2b1043a361c75d44791c79d5047aab9a) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '9c142673758b150198fe78e1f86fe86c') in 0.010011 seconds 
[ScriptCompilation] Recompiling all scripts because: InitialRefresh: Cached user assemblies were invalid
- Starting script compilation
- Starting compile Library/ScriptAssemblies/UnityEngine.TestRunner.dll
- Starting compile Library/ScriptAssemblies/UnityEngine.UI.dll
Deleting stamp file at Library/ScriptAssemblies/BuiltinAssemblies.stamp
- Starting compile Library/ScriptAssemblies/UnityEditor.TestRunner.dll
- Finished script compilation in 16.01011 seconds
Copying assembly from 'Temp/UnityEngine.TestRunner.dll' to 'Library/ScriptAssemblies/UnityEngine.TestRunner.dll' failed. Detailed error: Sharing violation on path 
AssetDatabase: script compilation time: 16.240833s
Scripts have compiler errors.
Exiting without the bug reporter. Application will terminate with return code 1