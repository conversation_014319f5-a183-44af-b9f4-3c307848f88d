Using pre-set license
Built from '2020.3/china_unity/release' branch; Version is '2020.3.48f1c1 (06fbdfbf16e3) revision 457695'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 16290 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
D:\Unity\2020.3.48f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/2024-5-15/UnityProject/SIPSorcerytest4.2
-logFile
Logs/AssetImportWorker0.log
-srvPort
9986
Successfully changed project path to: D:/2024-5-15/UnityProject/SIPSorcerytest4.2
D:/2024-5-15/UnityProject/SIPSorcerytest4.2
Using Asset Import Pipeline V2.
Player connection [19836] Host "[IP] ************* [Port] 805523936 [Flags] 2 [Guid] 737408629 [EditorId] 737408629 [Version] 1048832 [Id] WindowsEditor(7,xxzx-bzhzy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [19836] Host "[IP] ************* [Port] 805523936 [Flags] 2 [Guid] 737408629 [EditorId] 737408629 [Version] 1048832 [Id] WindowsEditor(7,xxzx-bzhzy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.Refreshing native plugins compatible for Editor in 41.64 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.48f1c1 (06fbdfbf16e3)
[Subsystems] Discovering subsystems at path D:/Unity/2020.3.48f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/2024-5-15/UnityProject/SIPSorcerytest4.2/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA GeForce GT 730 (ID=0x1287)
    Vendor:   
    VRAM:     2007 MB
    Driver:   30.0.14.7514
Initialize mono
Mono path[0] = 'D:/Unity/2020.3.48f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56472
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.005423 seconds.
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 33.35 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.803 seconds
Domain Reload Profiling:
	ReloadAssembly (803ms)
		BeginReloadAssembly (104ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (606ms)
			LoadAssemblies (103ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (174ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (44ms)
			SetupLoadedEditorAssemblies (249ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (33ms)
				BeforeProcessingInitializeOnLoad (21ms)
				ProcessInitializeOnLoadAttributes (136ms)
				ProcessInitializeOnLoadMethodAttributes (49ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.022220 seconds.
Begin MonoManager ReloadAssembly
Error: Could not load signature of SIPSorceryMedia.FFmpeg.FFmpegAudioDecoder:.ctor due to: Could not load file or assembly 'FFmpeg.AutoGen, Version=*******, Culture=neutral, PublicKeyToken=null' or one of its dependencies. assembly:FFmpeg.AutoGen, Version=*******, Culture=neutral, PublicKeyToken=null type:<unknown type> member:(null) signature:<none>
Unloading broken assembly Assets/Plugins/SIPSorceryMedia.FFmpeg.dll, this assembly can cause crashes in the runtime
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.38 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.470 seconds
Domain Reload Profiling:
	ReloadAssembly (1470ms)
		BeginReloadAssembly (208ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (24ms)
		EndReloadAssembly (1187ms)
			LoadAssemblies (167ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (355ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (544ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (150ms)
				ProcessInitializeOnLoadAttributes (355ms)
				ProcessInitializeOnLoadMethodAttributes (14ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.22 seconds
Refreshing native plugins compatible for Editor in 0.36 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1348 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 64.7 MB.
System memory in use after: 64.8 MB.

Unloading 30 unused Assets to reduce memory usage. Loaded Objects now: 1774.
Total: 3.610400 ms (FindLiveObjects: 0.225300 ms CreateObjectMapping: 0.127200 ms MarkObjects: 3.177900 ms  DeleteObjects: 0.078600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Resources/DefaultSIPConfig.asset
  artifactKey: Guid(781e4105998aac949bd0648cbb5cc15c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/DefaultSIPConfig.asset using Guid(781e4105998aac949bd0648cbb5cc15c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) Could not extract GUID in text file Assets/Resources/DefaultSIPConfig.asset at line 12.
Broken text PPtr. GUID 00000000000000000000000000000000 fileID 11500000 is invalid!
 -> (artifact id: 'df6a42dc6602c43fc2f875f035d556c5') in 0.097326 seconds 
