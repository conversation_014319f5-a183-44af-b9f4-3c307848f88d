<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SIPSorceryMedia.Abstractions</name>
    </assembly>
    <members>
        <member name="P:SIPSorceryMedia.Abstractions.IAudioEncoder.SupportedFormats">
            <summary>
            Needs to be set with the list of audio formats that the encoder supports.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.IAudioEncoder.EncodeAudio(System.Int16[],SIPSorceryMedia.Abstractions.AudioFormat)">
            <summary>
            Encodes 16bit signed PCM samples.
            </summary>
            <param name="pcm">An array of 16 bit signed audio samples.</param>
            <param name="format">The audio format to encode the PCM sample to.</param>
            <returns>A byte array containing the encoded sample.</returns>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.IAudioEncoder.DecodeAudio(System.Byte[],SIPSorceryMedia.Abstractions.AudioFormat)">
            <summary>
            Decodes to 16bit signed PCM samples.
            </summary>
            <param name="encodedSample">The byte array containing the encoded sample.</param>
            <param name="format">The audio format of the encoded sample.</param>
            <returns>An array containing the 16 bit signed PCM samples.</returns>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.ITextEncoder.EncodeText(System.Char[],SIPSorceryMedia.Abstractions.TextFormat)">
            <summary>
            Encode a text into a byte array.
            </summary>
            <param name="text">A symbol or text to be transmitted</param>
            <param name="format">The text format of the sample.</param>
            <returns>A byte array containing the encoded text sample</returns>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.ITextEncoder.DecodeText(System.Byte[],SIPSorceryMedia.Abstractions.TextFormat)">
            <summary>
            Decode a byte array into a string type text.
            </summary>
            <param name="encodedSample">A symbol or text that was received</param>
            <param name="format">The text format of the sample.</param>
            <returns></returns>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.IVideoEncoder.SupportedFormats">
            <summary>
            Needs to be set with the list of video formats that the encoder supports.
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.RawImage.Width">
            <summary>
            The width, in pixels, of the image
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.RawImage.Height">
            <summary>
            The height, in pixels, of the image
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.RawImage.Stride">
            <summary>
            Integer that specifies the byte offset between the beginning of one scan line and the next.
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.RawImage.Sample">
            <summary>
            Pointer to an array of bytes that contains the pixel data.
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.RawImage.PixelFormat">
            <summary>
            The pixel format of the image
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.RawImage.GetBuffer">
            <summary>
            Get bytes array of the image.
            
            For performance reasons it's better to use directly Sample
            </summary>
            <returns></returns>
        </member>
        <member name="T:SIPSorceryMedia.Abstractions.SDPWellKnownMediaFormatsEnum">
            <summary>
            A list of standard media formats that can be identified by an ID if
            there is no qualifying format attribute provided.
            </summary>
            <remarks>
            For definition of well known types see: https://tools.ietf.org/html/rfc3551#section-6.
            </remarks>
        </member>
        <member name="T:SIPSorceryMedia.Abstractions.EncodedAudioFrame">
            <summary>
            Represents an encoded media frame. Typically received from the RTP transport.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.IAudioEndPoint.Pause">
            <summary>
            Pauses the audio source and sink. The source will stop sending samples and the sink will stop receiving them.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.IAudioEndPoint.Resume">
            <summary>
            Resumes the audio source and sink. The source will start sending samples and the sink will start receiving them.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.IAudioEndPoint.Start">
            <summary>
            Starts the audio source and sink. The source will start sending samples and the sink will start receiving them.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.IAudioEndPoint.Close">
            <summary>
            Closes the audio source and sink. The source will stop sending samples and the sink will stop receiving them.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.ITextEndPoint.Pause">
            <summary>
            Pauses the text source and sink. The source will stop sending samples and the sink will stop receiving them.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.ITextEndPoint.Resume">
            <summary>
            Resumes the text source and sink. The source will start sending samples and the sink will start receiving them.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.ITextEndPoint.Start">
            <summary>
            Starts the text source and sink. The source will start sending samples and the sink will start receiving them.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.ITextEndPoint.Close">
            <summary>
            Closes the text source and sink. The source will stop sending samples and the sink will stop receiving them.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.IVideoEndPoint.Pause">
            <summary>
            Pauses the video source and sink. The source will stop sending frames and the sink will stop receiving them.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.IVideoEndPoint.Resume">
            <summary>
            Resumes the video source and sink. The source will start sending frames and the sink will start receiving them.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.IVideoEndPoint.Start">
            <summary>
            Starts the video source and sink. The source will start sending frames and the sink will start receiving them.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.IVideoEndPoint.Close">
            <summary>
            Closes the video source and sink. The source will stop sending frames and the sink will stop receiving them.
            </summary>
        </member>
        <member name="E:SIPSorceryMedia.Abstractions.IVideoSink.OnVideoSinkDecodedSample">
            <summary>
            This event will be fired by the sink after is decodes a video frame from the RTP stream.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.MediaFormatManager`1.RestrictFormats(System.Func{`0,System.Boolean})">
            <summary>
            Requests that the audio sink and source only advertise support for the supplied list of codecs.
            Only codecs that are already supported and in the <see cref="F:SIPSorceryMedia.Abstractions.MediaFormatManager`1.SupportedFormats" /> list can be 
            used.
            </summary>
            <param name="filter">Function to determine which formats the source formats should be restricted to.</param>
        </member>
        <member name="T:SIPSorceryMedia.Abstractions.AudioCommonlyUsedFormats">
            <summary>
            A list of audio formats that are commonly used but not standardised.
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.AudioCommonlyUsedFormats.OpusWebRTC">
            <summary>
            The Opus audio format typical used for WebRTC scenarios.
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.AudioFormat.FormatID">
            <summary>
            The format ID for the codec. If this is a well known codec it should be set to the
            value from the codec enum. If the codec is a dynamic it must be set between 96–127
            inclusive.
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.AudioFormat.FormatName">
            <summary>
            The official name for the codec. This field is critical for dynamic codecs
            where it is used to match the codecs in the SDP offer/answer.
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.AudioFormat.RtpClockRate">
            <summary>
            The rate used to set RTP timestamps and to be set in the SDP format
            attribute for this format. It should almost always be the same as the
            <seealso cref="P:SIPSorceryMedia.Abstractions.AudioFormat.ClockRate"/>. An example of where it's not is G722 which
            uses a sample rate of 16KHz but an RTP rate of 8KHz for historical reasons.
            </summary>
            <example>
            In the SDP format attribute below the RTP clock rate is 48000.
            a=rtpmap:109 opus/48000/2
            </example>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.AudioFormat.ClockRate">
            <summary>
            The rate used by decoded samples for this audio format.
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.AudioFormat.ChannelCount">
            <summary>
            The number of channels for the audio format.
            </summary>
            <example>
            In the SDP format attribute below the channel count is 2.
            Note for single channel codecs the parameter is typically omitted from the
            SDP format attribute.
            a=rtpmap:109 opus/48000/2
            </example>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.AudioFormat.Parameters">
            <summary>
            This is the string that goes in the SDP "a=fmtp" parameter.
            This field should be set WITHOUT the "a=fmtp:" prefix.
            </summary>
            <example>
            In the case below this filed should be set as "emphasis=50-15".
            a=fmtp:97 emphasis=50-15
            </example>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.AudioFormat.#ctor(SIPSorceryMedia.Abstractions.SDPWellKnownMediaFormatsEnum)">
            <summary>
            Creates a new audio format based on a well known SDP format.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.AudioFormat.#ctor(SIPSorceryMedia.Abstractions.AudioCodecsEnum,System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            Creates a new audio format based on a well known codec.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.AudioFormat.#ctor(SIPSorceryMedia.Abstractions.AudioCodecsEnum,System.Int32,System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            Creates a new audio format based on a well known codec.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.AudioFormat.#ctor(System.Int32,System.String,System.Int32,System.Int32,System.String)">
            <summary>
            Creates a new audio format based on a dynamic codec (or an unsupported well known codec).
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.AudioFormat.#ctor(System.Int32,System.String,System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            Creates a new audio format based on a dynamic codec (or an unsupported well known codec).
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.TextFormat.#ctor(System.Int32,System.String,System.Int32,System.String)">
            <summary>
            Creates a new text format based on a dynamic codec (or an unsupported well known codec).
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.TextFormat.FormatID">
            <summary>
            The format ID for the codec. If this is a well known codec it should be set to the
            value from the codec enum. If the codec is a dynamic it must be set between 96–127
            inclusive.
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.TextFormat.FormatName">
            <summary>
            The official name for the codec. This field is critical for dynamic codecs
            where it is used to match the codecs in the SDP offer/answer.
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.TextFormat.ClockRate">
            <summary>
            The rate used by decoded samples for this text format.
            </summary>
            <remarks>
            Example, 1000 is the clock rate:
            a=rtpmap:98 t140/1000
            </remarks>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.TextFormat.Parameters">
            <summary>
            This is the "a=fmtp" format parameter that will be set in the SDP offer/answer.
            This field should be set WITHOUT the "a=fmtp:0" prefix.
            </summary>
            <remarks>
            Example:
            a=fmtp:100 98/98/98
            </remarks>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.VideoFormat.FormatID">
            <summary>
            The format ID for the codec. If this is a well known codec it should be set to the
            value from the codec enum. If the codec is a dynamic it must be set between 96–127
            inclusive.
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.VideoFormat.FormatName">
            <summary>
            The official name for the codec. This field is critical for dynamic codecs
            where it is used to match the codecs in the SDP offer/answer.
            </summary>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.VideoFormat.ClockRate">
            <summary>
            The rate used by decoded samples for this video format.
            </summary>
            <remarks>
            Example, 90000 is the clock rate:
            a=rtpmap:102 H264/90000
            </remarks>
        </member>
        <member name="P:SIPSorceryMedia.Abstractions.VideoFormat.Parameters">
            <summary>
            This is the "a=fmtp" format parameter that will be set in the SDP offer/answer.
            This field should be set WITHOUT the "a=fmtp:0" prefix.
            </summary>
            <remarks>
            Example:
            a=fmtp:102 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f"
            </remarks>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.VideoFormat.#ctor(SIPSorceryMedia.Abstractions.SDPWellKnownMediaFormatsEnum)">
            <summary>
            Creates a new video format based on a well known SDP format.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.VideoFormat.#ctor(SIPSorceryMedia.Abstractions.VideoCodecsEnum,System.Int32,System.Int32,System.String)">
            <summary>
            Creates a new video format based on a well known codec.
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.VideoFormat.#ctor(System.Int32,System.String,System.Int32,System.String)">
            <summary>
            Creates a new video format based on a dynamic codec (or an unsupported well known codec).
            </summary>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.PixelConverter.ToI420(System.Int32,System.Int32,System.Int32,System.Byte[],SIPSorceryMedia.Abstractions.VideoPixelFormatsEnum)">
            <summary>
            Attempts to convert an image buffer into an I420 format.
            </summary>
            <param name="width">The width of the image in pixels.</param>
            <param name="height">The height of the image in pixels.</param>
            <param name="stride">The stride of the image. Currently this method can only convert RGB and BGR
            formats. For those formats the stride is typically: width x bytes per pixel. For example for
            a 640x480 RGB sample stride=640x3. For a 640x480 BGRA sample stride=640x4. Note in some cases 
            the stride could be greater than the width x bytes per pixel.</param>
            <param name="sample">The buffer containing the image data.</param>
            <param name="pixelFormat">The pixel format of the image.</param>
            <returns>If successful a buffer containing an I420 formatted image sample.</returns>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.PixelConverter.RGBAtoI420(System.Byte[],System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Converts an RGBA sample to an I420 formatted sample.
            </summary>
            <param name="rgba">The RGBA image sample.</param>
            <param name="width">The width in pixels of the RGBA sample.</param>
            <param name="height">The height in pixels of the RGBA sample.</param>
            <param name="stride">The stride of the RGBA sample.</param>
            <param name="dop">The degree of parallelism for converting.</param>
            <returns>An I420 buffer representing the source image.</returns>
            <remarks>
            https://docs.microsoft.com/en-us/previous-versions/visualstudio/hh394035(v=vs.105)
            http://qiita.com/gomachan7/items/54d43693f943a0986e95
            </remarks>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.PixelConverter.RGBtoI420(System.Byte[],System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Converts an RGB sample to an I420 formatted sample.
            </summary>
            <param name="rgb">The RGB image sample.</param>
            <param name="width">The width in pixels of the RGB sample.</param>
            <param name="height">The height in pixels of the RGB sample.</param>
            <param name="stride">The stride of the RGB sample.</param>
            <param name="dop">The degree of parallelism for converting.</param>
            <returns>An I420 buffer representing the source image.</returns>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.PixelConverter.BGRtoI420(System.Byte[],System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Converts a BGR sample to an I420 formatted sample.
            </summary>
            <param name="bgr">The BGR image sample.</param>
            <param name="width">The width in pixels of the BGR sample.</param>
            <param name="height">The height in pixels of the BGR sample.</param>
            <param name="stride">The stride of the BGR sample.</param>
            <param name="dop">The degree of parallelism for converting.</param>
            <returns>An I420 buffer representing the source image.</returns>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.PixelConverter.BGRAtoI420(System.Byte[],System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Converts a BGRA sample to an I420 formatted sample.
            </summary>
            <param name="bgra">The BGRA image sample.</param>
            <param name="width">The width in pixels of the BGRA sample.</param>
            <param name="height">The height in pixels of the BGRA sample.</param>
            <param name="stride">The stride of the BGRA sample.</param>
            <param name="dop">The degree of parallelism for converting.</param>
            <returns>An I420 buffer representing the source image.</returns>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.PixelConverter.I420toRGB(System.Byte[],System.Int32,System.Int32,System.Int32@,System.Int32)">
            <summary>
            Converts an I420 sample to an RGB formatted sample.
            </summary>
            <param name="data">The I420 image sample.</param>
            <param name="width">The width in pixels of the I420 sample.</param>
            <param name="height">The height in pixels of the I420 sample.</param>
            <param name="stride">The stride to use for the desintation RGB sample.</param>
            <param name="dop">The degree of parallelism for converting.</param>
            <returns>An RGB buffer representing the source image.</returns>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.PixelConverter.I420toBGR(System.Byte[],System.Int32,System.Int32,System.Int32@,System.Int32)">
            <summary>
            Converts an I420 sample to an BGR formatted sample.
            </summary>
            <param name="data">The I420 image sample.</param>
            <param name="width">The width in pixels of the I420 sample.</param>
            <param name="height">The height in pixels of the I420 sample.</param>
            <param name="stride">The stride to use for the desintation BGR sample.</param>
            <param name="dop">The degree of parallelism for converting.</param>
            <returns>A BGR buffer representing the source image.</returns>
        </member>
        <member name="M:SIPSorceryMedia.Abstractions.PixelConverter.NV12toBGR(System.Byte[],System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Converts an NV12 sample to an BGR formatted sample.
            </summary>
            <param name="data">The NV12 image sample.</param>
            <param name="width">The width in pixels of the NV12 sample.</param>
            <param name="height">The height in pixels of the NV12 sample.</param>
            <param name="stride">The stride to use for the desintation BGR sample.</param>
            <param name="dop">The degree of parallelism for converting.</param>
            <returns>A BGR buffer representing the source image.</returns>
        </member>
    </members>
</doc>
