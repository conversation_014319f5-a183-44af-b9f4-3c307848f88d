﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="DnsClient" version="1.7.0" targetFramework="net471" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net471" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.0" targetFramework="net471" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="8.0.0" targetFramework="net471" />
  <package id="Microsoft.Win32.Registry" version="5.0.0" targetFramework="net471" />
  <package id="Portable.BouncyCastle" version="1.9.0" targetFramework="net471" />
  <package id="SIPSorcery" version="8.0.6" targetFramework="net471" />
  <package id="SIPSorcery.WebSocketSharp" version="0.0.1" targetFramework="net471" />
  <package id="SIPSorceryMedia.Abstractions" version="8.0.12" targetFramework="net471" />
  <package id="SIPSorceryMedia.FFmpeg" version="8.0.12" targetFramework="net471" />
  <package id="FFMpegCore" version="5.1.0" targetFramework="net471" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net471" />
  <package id="System.IO" version="4.3.0" targetFramework="net471" />
  <package id="System.Memory" version="4.5.5" targetFramework="net471" />
  <package id="System.Net.WebSockets" version="4.3.0" targetFramework="net471" />
  <package id="System.Net.WebSockets.Client" version="4.3.2" targetFramework="net471" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net471" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net471" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net471" />
  <package id="System.Security.AccessControl" version="5.0.0" targetFramework="net471" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net471" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net471" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net471" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net471" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net471" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net471" />
</packages>